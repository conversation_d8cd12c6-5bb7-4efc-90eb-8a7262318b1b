#!/usr/bin/env python3
"""
Test runner script for WritingWay project
Runs backend and frontend tests with proper setup and reporting
"""

import os
import sys
import subprocess
import argparse
import time
from pathlib import Path


class TestRunner:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.backend_dir = self.project_root / "backend"
        self.frontend_dir = self.project_root / "frontend"
        
    def run_command(self, command, cwd=None, env=None):
        """Run a command and return the result"""
        print(f"Running: {' '.join(command)}")
        print(f"Working directory: {cwd or os.getcwd()}")
        
        try:
            result = subprocess.run(
                command,
                cwd=cwd,
                env=env,
                capture_output=True,
                text=True,
                check=True
            )
            print(f"✅ Command succeeded")
            return True, result.stdout, result.stderr
        except subprocess.CalledProcessError as e:
            print(f"❌ Command failed with exit code {e.returncode}")
            print(f"STDOUT: {e.stdout}")
            print(f"STDERR: {e.stderr}")
            return False, e.stdout, e.stderr
    
    def setup_backend_env(self):
        """Setup backend testing environment"""
        print("\n🔧 Setting up backend testing environment...")
        
        # Check if virtual environment exists
        venv_path = self.backend_dir / "venv"
        if not venv_path.exists():
            print("Creating virtual environment...")
            success, _, _ = self.run_command([
                sys.executable, "-m", "venv", "venv"
            ], cwd=self.backend_dir)
            if not success:
                return False
        
        # Install dependencies
        pip_cmd = str(venv_path / "Scripts" / "pip.exe") if os.name == 'nt' else str(venv_path / "bin" / "pip")
        print("Installing backend dependencies...")
        success, _, _ = self.run_command([
            pip_cmd, "install", "-r", "requirements.txt"
        ], cwd=self.backend_dir)
        
        return success
    
    def setup_frontend_env(self):
        """Setup frontend testing environment"""
        print("\n🔧 Setting up frontend testing environment...")
        
        # Check if node_modules exists
        node_modules = self.frontend_dir / "node_modules"
        if not node_modules.exists():
            print("Installing frontend dependencies...")
            success, _, _ = self.run_command([
                "npm", "install"
            ], cwd=self.frontend_dir)
            if not success:
                return False
        
        return True
    
    def run_backend_tests(self, test_type="all", coverage=True):
        """Run backend tests"""
        print(f"\n🧪 Running backend {test_type} tests...")
        
        # Prepare pytest command
        venv_path = self.backend_dir / "venv"
        pytest_cmd = str(venv_path / "Scripts" / "pytest.exe") if os.name == 'nt' else str(venv_path / "bin" / "pytest")
        
        cmd = [pytest_cmd]
        
        if test_type == "unit":
            cmd.extend(["tests/unit/"])
        elif test_type == "integration":
            cmd.extend(["tests/integration/"])
        elif test_type == "e2e":
            cmd.extend(["tests/e2e/"])
        else:
            cmd.extend(["tests/"])
        
        if coverage:
            cmd.extend([
                "--cov=.",
                "--cov-report=html:htmlcov",
                "--cov-report=term-missing",
                "--cov-report=xml"
            ])
        
        cmd.extend(["-v", "--tb=short"])
        
        # Set environment variables
        env = os.environ.copy()
        env.update({
            "PYTHONPATH": str(self.backend_dir),
            "TESTING": "true",
            "DATABASE_URL": "sqlite:///./test.db"
        })
        
        success, stdout, stderr = self.run_command(cmd, cwd=self.backend_dir, env=env)
        
        if success:
            print("✅ Backend tests passed!")
            if coverage:
                print(f"📊 Coverage report generated at: {self.backend_dir}/htmlcov/index.html")
        else:
            print("❌ Backend tests failed!")
        
        return success
    
    def run_frontend_tests(self, test_type="all", coverage=True):
        """Run frontend tests"""
        print(f"\n🧪 Running frontend {test_type} tests...")
        
        if test_type == "unit" or test_type == "all":
            cmd = ["npm", "test"]
            if coverage:
                cmd = ["npm", "run", "test:coverage"]
            
            # Set CI environment to avoid interactive mode
            env = os.environ.copy()
            env["CI"] = "true"
            
            success, stdout, stderr = self.run_command(cmd, cwd=self.frontend_dir, env=env)
            
            if not success:
                print("❌ Frontend unit tests failed!")
                return False
        
        if test_type == "e2e" or test_type == "all":
            print("🌐 Running E2E tests with Cypress...")
            
            # Start the development server
            print("Starting development server...")
            server_process = subprocess.Popen(
                ["npm", "start"],
                cwd=self.frontend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # Wait for server to start
            time.sleep(10)
            
            try:
                # Run Cypress tests
                success, stdout, stderr = self.run_command([
                    "npm", "run", "cypress:run"
                ], cwd=self.frontend_dir)
                
                if not success:
                    print("❌ Frontend E2E tests failed!")
                    return False
            finally:
                # Stop the development server
                server_process.terminate()
                server_process.wait()
        
        print("✅ Frontend tests passed!")
        if coverage and (test_type == "unit" or test_type == "all"):
            print(f"📊 Coverage report generated at: {self.frontend_dir}/coverage/lcov-report/index.html")
        
        return True
    
    def run_integration_tests(self):
        """Run full integration tests"""
        print("\n🔗 Running full integration tests...")
        
        # Start backend server
        print("Starting backend server...")
        venv_path = self.backend_dir / "venv"
        python_cmd = str(venv_path / "Scripts" / "python.exe") if os.name == 'nt' else str(venv_path / "bin" / "python")
        
        backend_process = subprocess.Popen(
            [python_cmd, "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"],
            cwd=self.backend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # Start frontend server
        print("Starting frontend server...")
        frontend_process = subprocess.Popen(
            ["npm", "start"],
            cwd=self.frontend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # Wait for servers to start
        time.sleep(15)
        
        try:
            # Run integration tests
            success = self.run_backend_tests("integration", coverage=False)
            if success:
                success = self.run_frontend_tests("e2e", coverage=False)
            
            return success
        finally:
            # Stop servers
            backend_process.terminate()
            frontend_process.terminate()
            backend_process.wait()
            frontend_process.wait()
    
    def generate_test_report(self):
        """Generate comprehensive test report"""
        print("\n📋 Generating test report...")
        
        report_dir = self.project_root / "test-reports"
        report_dir.mkdir(exist_ok=True)
        
        # Combine coverage reports if they exist
        backend_coverage = self.backend_dir / "coverage.xml"
        frontend_coverage = self.frontend_dir / "coverage" / "lcov.info"
        
        report_content = []
        report_content.append("# WritingWay Test Report\n")
        report_content.append(f"Generated at: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        if backend_coverage.exists():
            report_content.append("## Backend Coverage\n")
            report_content.append(f"- Coverage report: {self.backend_dir}/htmlcov/index.html\n")
            report_content.append(f"- XML report: {backend_coverage}\n\n")
        
        if frontend_coverage.exists():
            report_content.append("## Frontend Coverage\n")
            report_content.append(f"- Coverage report: {self.frontend_dir}/coverage/lcov-report/index.html\n")
            report_content.append(f"- LCOV report: {frontend_coverage}\n\n")
        
        report_content.append("## Test Locations\n")
        report_content.append("### Backend Tests\n")
        report_content.append("- Unit tests: `backend/tests/unit/`\n")
        report_content.append("- Integration tests: `backend/tests/integration/`\n")
        report_content.append("- E2E tests: `backend/tests/e2e/`\n\n")
        
        report_content.append("### Frontend Tests\n")
        report_content.append("- Unit tests: `frontend/src/__tests__/`\n")
        report_content.append("- E2E tests: `frontend/cypress/e2e/`\n\n")
        
        report_content.append("## Running Tests\n")
        report_content.append("```bash\n")
        report_content.append("# Run all tests\n")
        report_content.append("python run_tests.py --all\n\n")
        report_content.append("# Run specific test types\n")
        report_content.append("python run_tests.py --backend --unit\n")
        report_content.append("python run_tests.py --frontend --e2e\n")
        report_content.append("```\n")
        
        report_file = report_dir / "test-report.md"
        with open(report_file, "w") as f:
            f.writelines(report_content)
        
        print(f"📋 Test report generated: {report_file}")


def main():
    parser = argparse.ArgumentParser(description="Run WritingWay tests")
    parser.add_argument("--backend", action="store_true", help="Run backend tests")
    parser.add_argument("--frontend", action="store_true", help="Run frontend tests")
    parser.add_argument("--integration", action="store_true", help="Run integration tests")
    parser.add_argument("--all", action="store_true", help="Run all tests")
    parser.add_argument("--unit", action="store_true", help="Run unit tests only")
    parser.add_argument("--e2e", action="store_true", help="Run E2E tests only")
    parser.add_argument("--no-coverage", action="store_true", help="Skip coverage reporting")
    parser.add_argument("--setup-only", action="store_true", help="Only setup environments")
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    # Default to running all tests if no specific option is provided
    if not any([args.backend, args.frontend, args.integration, args.all]):
        args.all = True
    
    success = True
    coverage = not args.no_coverage
    
    try:
        # Setup environments
        if args.backend or args.all or args.integration:
            if not runner.setup_backend_env():
                print("❌ Failed to setup backend environment")
                return 1
        
        if args.frontend or args.all or args.integration:
            if not runner.setup_frontend_env():
                print("❌ Failed to setup frontend environment")
                return 1
        
        if args.setup_only:
            print("✅ Environment setup completed")
            return 0
        
        # Run tests
        if args.integration:
            success = runner.run_integration_tests()
        else:
            if args.backend or args.all:
                test_type = "unit" if args.unit else "e2e" if args.e2e else "all"
                if not runner.run_backend_tests(test_type, coverage):
                    success = False
            
            if args.frontend or args.all:
                test_type = "unit" if args.unit else "e2e" if args.e2e else "all"
                if not runner.run_frontend_tests(test_type, coverage):
                    success = False
        
        # Generate report
        runner.generate_test_report()
        
        if success:
            print("\n🎉 All tests passed!")
            return 0
        else:
            print("\n💥 Some tests failed!")
            return 1
            
    except KeyboardInterrupt:
        print("\n⏹️ Tests interrupted by user")
        return 1
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
