const { defineConfig } = require('cypress');

module.exports = defineConfig({
  e2e: {
    baseUrl: 'http://localhost:3000',
    supportFile: 'cypress/support/e2e.js',
    specPattern: 'cypress/e2e/**/*.cy.{js,jsx,ts,tsx}',
    viewportWidth: 1280,
    viewportHeight: 720,
    video: true,
    screenshotOnRunFailure: true,
    defaultCommandTimeout: 10000,
    requestTimeout: 10000,
    responseTimeout: 10000,
    
    setupNodeEvents(on, config) {
      // implement node event listeners here
      
      // Code coverage
      require('@cypress/code-coverage/task')(on, config);
      
      // Environment-specific configuration
      if (config.env.environment === 'staging') {
        config.baseUrl = 'https://staging.writingway.com';
      } else if (config.env.environment === 'production') {
        config.baseUrl = 'https://writingway.com';
      }
      
      return config;
    },
    
    env: {
      // API endpoints
      apiUrl: 'http://localhost:8000/api',
      
      // Test user credentials
      testUser: {
        username: 'admin',
        password: 'admin123',
        email: '<EMAIL>'
      },
      
      // Feature flags for testing
      features: {
        aiAssistant: true,
        collaboration: true,
        export: true,
        analytics: true
      },
      
      // Test data
      testProject: {
        name: 'Cypress Test Project',
        description: 'A project created by Cypress tests'
      }
    }
  },
  
  component: {
    devServer: {
      framework: 'create-react-app',
      bundler: 'webpack',
    },
    supportFile: 'cypress/support/component.js',
    specPattern: 'src/**/*.cy.{js,jsx,ts,tsx}',
    indexHtmlFile: 'cypress/support/component-index.html'
  }
});
