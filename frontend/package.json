{"name": "writingway-frontend", "version": "2.0.0", "private": true, "dependencies": {"@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@mui/icons-material": "^5.11.9", "@mui/lab": "^5.0.0-alpha.120", "@mui/material": "^5.11.10", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.3.4", "date-fns": "^2.29.3", "lodash": "^4.17.21", "quill": "^1.3.7", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-hook-form": "^7.43.5", "react-hot-toast": "^2.4.0", "react-query": "^3.39.3", "react-quill": "^2.0.0", "react-router-dom": "^6.8.1", "react-scripts": "5.0.1", "zustand": "^4.3.6"}, "devDependencies": {"@testing-library/react-hooks": "^8.0.1", "cypress": "^13.6.0", "jest-environment-jsdom": "^29.7.0", "msw": "^2.10.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "test:coverage": "react-scripts test --coverage --watchAll=false", "test:ci": "CI=true react-scripts test --coverage --watchAll=false", "cypress:open": "cypress open", "cypress:run": "cypress run", "test:e2e": "cypress run", "test:e2e:open": "cypress open", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8000"}