// jest-dom adds custom jest matchers for asserting on DOM nodes.
// allows you to do things like:
// expect(element).toHaveTextContent(/react/i)
// learn more: https://github.com/testing-library/jest-dom
import '@testing-library/jest-dom';

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
};

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
};

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock scrollTo
Object.defineProperty(window, 'scrollTo', {
  writable: true,
  value: jest.fn(),
});

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

// Mock sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.sessionStorage = sessionStorageMock;

// Mock URL.createObjectURL
global.URL.createObjectURL = jest.fn(() => 'mocked-url');
global.URL.revokeObjectURL = jest.fn();

// Mock fetch
global.fetch = jest.fn();

// Setup MSW (Mock Service Worker) for API mocking
import { setupServer } from 'msw/node';
import { rest } from 'msw';

// Define request handlers
export const handlers = [
  // Auth endpoints
  rest.post('/api/auth/login', (req, res, ctx) => {
    return res(
      ctx.json({
        access_token: 'mock-token',
        token_type: 'bearer',
        user: {
          id: 1,
          username: 'testuser',
          email: '<EMAIL>',
          full_name: 'Test User'
        }
      })
    );
  }),

  rest.post('/api/auth/register', (req, res, ctx) => {
    return res(
      ctx.status(201),
      ctx.json({
        id: 1,
        username: 'newuser',
        email: '<EMAIL>',
        full_name: 'New User'
      })
    );
  }),

  rest.get('/api/auth/me', (req, res, ctx) => {
    return res(
      ctx.json({
        id: 1,
        username: 'testuser',
        email: '<EMAIL>',
        full_name: 'Test User'
      })
    );
  }),

  // Projects endpoints
  rest.get('/api/projects/', (req, res, ctx) => {
    return res(
      ctx.json([
        {
          id: 1,
          name: 'Test Project',
          description: 'A test project',
          created_at: '2024-01-01T00:00:00Z'
        }
      ])
    );
  }),

  rest.post('/api/projects/', (req, res, ctx) => {
    return res(
      ctx.status(201),
      ctx.json({
        id: 2,
        name: 'New Project',
        description: 'A new project',
        created_at: '2024-01-01T00:00:00Z'
      })
    );
  }),

  // AI endpoints
  rest.post('/api/ai/chat', (req, res, ctx) => {
    return res(
      ctx.json({
        response: 'This is a mock AI response for testing.'
      })
    );
  }),

  rest.post('/api/ai/writing-assistance', (req, res, ctx) => {
    return res(
      ctx.json({
        response: 'This is mock writing assistance.'
      })
    );
  }),

  rest.post('/api/ai/story-prompt', (req, res, ctx) => {
    return res(
      ctx.json({
        prompt: 'Write a story about a brave adventurer who discovers a hidden treasure.'
      })
    );
  }),
];

// Setup server
export const server = setupServer(...handlers);

// Establish API mocking before all tests
beforeAll(() => server.listen());

// Reset any request handlers that we may add during the tests,
// so they don't affect other tests
afterEach(() => server.resetHandlers());

// Clean up after the tests are finished
afterAll(() => server.close());
