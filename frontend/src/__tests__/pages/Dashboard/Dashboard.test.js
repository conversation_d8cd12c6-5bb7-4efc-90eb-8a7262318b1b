/**
 * Integration tests for Dashboard page
 */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from 'react-query';
import Dashboard from '../../../pages/Dashboard/Dashboard';
import { useAuthStore } from '../../../stores/authStore';
import * as projectService from '../../../services/projectService';

// Mock dependencies
jest.mock('../../../stores/authStore');
jest.mock('../../../services/projectService');

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// Test wrapper component
const TestWrapper = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('Dashboard Page', () => {
  const mockUser = {
    id: 1,
    username: 'testuser',
    email: '<EMAIL>',
    full_name: 'Test User',
  };

  const mockProjects = [
    {
      id: 1,
      name: 'Fantasy Adventure',
      description: 'An epic fantasy story',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-02T00:00:00Z',
    },
    {
      id: 2,
      name: 'Sci-Fi Novel',
      description: 'A futuristic tale',
      created_at: '2024-01-03T00:00:00Z',
      updated_at: '2024-01-04T00:00:00Z',
    },
  ];

  beforeEach(() => {
    useAuthStore.mockReturnValue({
      user: mockUser,
      isAuthenticated: true,
    });

    projectService.getProjects.mockResolvedValue(mockProjects);
    projectService.createProject.mockResolvedValue({
      id: 3,
      name: 'New Project',
      description: 'A new project',
    });

    jest.clearAllMocks();
  });

  it('renders dashboard with user greeting', async () => {
    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );

    expect(screen.getByText(/welcome back, test user/i)).toBeInTheDocument();
    expect(screen.getByText(/your writing projects/i)).toBeInTheDocument();
  });

  it('displays user projects correctly', async () => {
    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Fantasy Adventure')).toBeInTheDocument();
      expect(screen.getByText('Sci-Fi Novel')).toBeInTheDocument();
      expect(screen.getByText('An epic fantasy story')).toBeInTheDocument();
      expect(screen.getByText('A futuristic tale')).toBeInTheDocument();
    });
  });

  it('shows create new project button', () => {
    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );

    expect(screen.getByText(/create new project/i)).toBeInTheDocument();
  });

  it('opens create project dialog when button is clicked', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );

    const createButton = screen.getByText(/create new project/i);
    await user.click(createButton);

    expect(screen.getByText(/new project/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/project name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/description/i)).toBeInTheDocument();
  });

  it('creates new project successfully', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );

    // Open create dialog
    const createButton = screen.getByText(/create new project/i);
    await user.click(createButton);

    // Fill form
    const nameInput = screen.getByLabelText(/project name/i);
    const descriptionInput = screen.getByLabelText(/description/i);
    
    await user.type(nameInput, 'New Project');
    await user.type(descriptionInput, 'A new project description');

    // Submit form
    const submitButton = screen.getByRole('button', { name: /create/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(projectService.createProject).toHaveBeenCalledWith({
        name: 'New Project',
        description: 'A new project description',
      });
    });
  });

  it('navigates to project when project card is clicked', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Fantasy Adventure')).toBeInTheDocument();
    });

    const projectCard = screen.getByText('Fantasy Adventure').closest('[data-testid="project-card"]');
    await user.click(projectCard);

    expect(mockNavigate).toHaveBeenCalledWith('/projects/1');
  });

  it('displays empty state when no projects exist', async () => {
    projectService.getProjects.mockResolvedValue([]);

    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText(/no projects yet/i)).toBeInTheDocument();
      expect(screen.getByText(/create your first project/i)).toBeInTheDocument();
    });
  });

  it('shows loading state while fetching projects', () => {
    projectService.getProjects.mockReturnValue(new Promise(() => {})); // Never resolves

    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );

    expect(screen.getByText(/loading/i)).toBeInTheDocument();
  });

  it('handles project fetch error gracefully', async () => {
    projectService.getProjects.mockRejectedValue(new Error('Failed to fetch projects'));

    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText(/error loading projects/i)).toBeInTheDocument();
    });
  });

  it('displays recent activity section', async () => {
    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );

    expect(screen.getByText(/recent activity/i)).toBeInTheDocument();
  });

  it('shows writing statistics', async () => {
    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText(/total projects/i)).toBeInTheDocument();
      expect(screen.getByText('2')).toBeInTheDocument(); // Number of projects
    });
  });

  it('displays quick actions section', () => {
    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );

    expect(screen.getByText(/quick actions/i)).toBeInTheDocument();
    expect(screen.getByText(/ai story prompt/i)).toBeInTheDocument();
    expect(screen.getByText(/writing tips/i)).toBeInTheDocument();
  });

  it('handles AI story prompt generation', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );

    const promptButton = screen.getByText(/ai story prompt/i);
    await user.click(promptButton);

    // Should open AI prompt dialog or navigate to AI assistant
    expect(screen.getByText(/generating prompt/i) || mockNavigate).toBeTruthy();
  });

  it('supports project search functionality', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Fantasy Adventure')).toBeInTheDocument();
    });

    const searchInput = screen.getByPlaceholderText(/search projects/i);
    await user.type(searchInput, 'Fantasy');

    // Should filter projects
    expect(screen.getByText('Fantasy Adventure')).toBeInTheDocument();
    expect(screen.queryByText('Sci-Fi Novel')).not.toBeInTheDocument();
  });

  it('supports project sorting', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Fantasy Adventure')).toBeInTheDocument();
    });

    const sortSelect = screen.getByLabelText(/sort by/i);
    await user.selectOptions(sortSelect, 'name');

    // Projects should be sorted by name
    const projectCards = screen.getAllByTestId('project-card');
    expect(projectCards[0]).toHaveTextContent('Fantasy Adventure');
    expect(projectCards[1]).toHaveTextContent('Sci-Fi Novel');
  });

  it('displays project context menu on right click', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Fantasy Adventure')).toBeInTheDocument();
    });

    const projectCard = screen.getByText('Fantasy Adventure').closest('[data-testid="project-card"]');
    await user.pointer({ keys: '[MouseRight]', target: projectCard });

    expect(screen.getByText(/edit project/i)).toBeInTheDocument();
    expect(screen.getByText(/delete project/i)).toBeInTheDocument();
    expect(screen.getByText(/duplicate project/i)).toBeInTheDocument();
  });

  it('handles project deletion', async () => {
    const user = userEvent.setup();
    projectService.deleteProject.mockResolvedValue();

    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Fantasy Adventure')).toBeInTheDocument();
    });

    // Open context menu
    const projectCard = screen.getByText('Fantasy Adventure').closest('[data-testid="project-card"]');
    await user.pointer({ keys: '[MouseRight]', target: projectCard });

    // Click delete
    const deleteButton = screen.getByText(/delete project/i);
    await user.click(deleteButton);

    // Confirm deletion
    const confirmButton = screen.getByText(/confirm delete/i);
    await user.click(confirmButton);

    await waitFor(() => {
      expect(projectService.deleteProject).toHaveBeenCalledWith(1);
    });
  });

  it('shows keyboard shortcuts help', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );

    // Press help shortcut
    await user.keyboard('{?}');

    expect(screen.getByText(/keyboard shortcuts/i)).toBeInTheDocument();
    expect(screen.getByText(/ctrl\+n.*new project/i)).toBeInTheDocument();
  });

  it('supports keyboard navigation', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Fantasy Adventure')).toBeInTheDocument();
    });

    // Use keyboard to navigate
    await user.keyboard('{Tab}');
    await user.keyboard('{Enter}');

    // Should navigate to first project
    expect(mockNavigate).toHaveBeenCalledWith('/projects/1');
  });
});
