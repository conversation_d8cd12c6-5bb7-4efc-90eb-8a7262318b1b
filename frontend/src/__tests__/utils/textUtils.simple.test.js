/**
 * Simple unit tests for text utility functions
 */
import {
  countWords,
  cleanMarkdownFormatting,
  cleanAIResponse,
  formatTextForDisplay,
  stripHtmlTags
} from '../../utils/textUtils';

describe('Text Utilities', () => {
  describe('countWords', () => {
    it('should count words correctly in simple text', () => {
      expect(countWords('Hello world this is a test')).toBe(6);
    });

    it('should handle empty string', () => {
      expect(countWords('')).toBe(0);
    });

    it('should handle null or undefined', () => {
      expect(countWords(null)).toBe(0);
      expect(countWords(undefined)).toBe(0);
    });
  });

  describe('cleanMarkdownFormatting', () => {
    it('should remove bold formatting', () => {
      expect(cleanMarkdownFormatting('**bold text**')).toBe('bold text');
    });

    it('should handle empty input', () => {
      expect(cleanMarkdownFormatting('')).toBe('');
    });
  });

  describe('stripHtmlTags', () => {
    it('should remove HTML tags', () => {
      const input = '<p>Hello <b>world</b></p>';
      const result = stripHtmlTags(input);
      expect(result).toBe('Hello world');
    });

    it('should handle empty input', () => {
      expect(stripHtmlTags('')).toBe('');
    });
  });
});
