/**
 * Unit tests for text utility functions
 */
import {
  countWords,
  extractKeywords,
  sanitizeText,
  formatText,
  calculateReadingTime,
  highlightText,
  truncateText,
  capitalizeWords,
  removeExtraSpaces,
  convertToSlug
} from '../../utils/textUtils';

describe('Text Utilities', () => {
  describe('countWords', () => {
    it('should count words correctly in simple text', () => {
      expect(countWords('Hello world this is a test')).toBe(6);
    });

    it('should handle empty string', () => {
      expect(countWords('')).toBe(0);
    });

    it('should handle whitespace only', () => {
      expect(countWords('   \n\t  ')).toBe(0);
    });

    it('should handle punctuation correctly', () => {
      expect(countWords('Hello, world! This is a test.')).toBe(6);
    });

    it('should handle multiple spaces', () => {
      expect(countWords('Hello    world     test')).toBe(3);
    });

    it('should handle newlines and tabs', () => {
      expect(countWords('Hello\nworld\ttest')).toBe(3);
    });

    it('should handle contractions as single words', () => {
      expect(countWords("don't can't won't")).toBe(3);
    });

    it('should handle hyphenated words', () => {
      expect(countWords('twenty-one self-aware')).toBe(2);
    });
  });

  describe('extractKeywords', () => {
    it('should extract keywords from text', () => {
      const text = 'The brave knight fought the dragon in the castle';
      const keywords = extractKeywords(text, 3);
      
      expect(keywords).toHaveLength(3);
      expect(keywords).toEqual(expect.arrayContaining(['knight', 'dragon', 'castle']));
    });

    it('should filter out common stopwords', () => {
      const text = 'The quick brown fox jumps over the lazy dog';
      const keywords = extractKeywords(text);
      
      const stopwords = ['the', 'over', 'a', 'an', 'and', 'or', 'but'];
      const hasStopwords = keywords.some(keyword => 
        stopwords.includes(keyword.toLowerCase())
      );
      
      expect(hasStopwords).toBe(false);
    });

    it('should handle empty text', () => {
      expect(extractKeywords('')).toEqual([]);
    });

    it('should return unique keywords', () => {
      const text = 'test test test word word';
      const keywords = extractKeywords(text);
      
      expect(keywords).toEqual(['test', 'word']);
    });

    it('should respect max keywords limit', () => {
      const text = 'one two three four five six seven eight';
      const keywords = extractKeywords(text, 3);
      
      expect(keywords).toHaveLength(3);
    });
  });

  describe('sanitizeText', () => {
    it('should remove script tags', () => {
      const text = "Hello <script>alert('xss')</script> world";
      const sanitized = sanitizeText(text);
      
      expect(sanitized).not.toContain('<script>');
      expect(sanitized).not.toContain('alert');
      expect(sanitized).toContain('Hello');
      expect(sanitized).toContain('world');
    });

    it('should remove HTML tags when not allowed', () => {
      const text = '<p>Hello <b>world</b></p>';
      const sanitized = sanitizeText(text, { allowHtml: false });
      
      expect(sanitized).toBe('Hello world');
    });

    it('should preserve safe HTML tags when allowed', () => {
      const text = '<p>Hello <b>world</b> <script>alert("xss")</script></p>';
      const sanitized = sanitizeText(text, { allowHtml: true });
      
      expect(sanitized).toContain('<p>');
      expect(sanitized).toContain('<b>');
      expect(sanitized).not.toContain('<script>');
    });

    it('should handle unicode characters', () => {
      const text = 'Hello 世界 🌍';
      const sanitized = sanitizeText(text);
      
      expect(sanitized).toContain('Hello');
      expect(sanitized).toContain('世界');
      expect(sanitized).toContain('🌍');
    });

    it('should remove dangerous attributes', () => {
      const text = '<img src="test.jpg" onerror="alert(\'xss\')" alt="test">';
      const sanitized = sanitizeText(text, { allowHtml: true });
      
      expect(sanitized).not.toContain('onerror');
      expect(sanitized).toContain('src');
      expect(sanitized).toContain('alt');
    });
  });

  describe('formatText', () => {
    it('should apply bold formatting', () => {
      const result = formatText('Hello world', 'bold');
      expect(result).toBe('<strong>Hello world</strong>');
    });

    it('should apply italic formatting', () => {
      const result = formatText('Hello world', 'italic');
      expect(result).toBe('<em>Hello world</em>');
    });

    it('should apply underline formatting', () => {
      const result = formatText('Hello world', 'underline');
      expect(result).toBe('<u>Hello world</u>');
    });

    it('should handle unknown formatting', () => {
      const result = formatText('Hello world', 'unknown');
      expect(result).toBe('Hello world');
    });

    it('should handle empty text', () => {
      const result = formatText('', 'bold');
      expect(result).toBe('');
    });
  });

  describe('calculateReadingTime', () => {
    it('should calculate reading time correctly', () => {
      const text = 'word '.repeat(200); // 200 words
      const readingTime = calculateReadingTime(text);
      
      expect(readingTime).toBe(1); // ~1 minute at 200 WPM
    });

    it('should handle short text', () => {
      const text = 'Hello world';
      const readingTime = calculateReadingTime(text);
      
      expect(readingTime).toBe(1); // Minimum 1 minute
    });

    it('should handle empty text', () => {
      const readingTime = calculateReadingTime('');
      expect(readingTime).toBe(0);
    });

    it('should use custom reading speed', () => {
      const text = 'word '.repeat(300); // 300 words
      const readingTime = calculateReadingTime(text, 300); // 300 WPM
      
      expect(readingTime).toBe(1);
    });
  });

  describe('highlightText', () => {
    it('should highlight search terms', () => {
      const text = 'The quick brown fox';
      const searchTerm = 'quick';
      const highlighted = highlightText(text, searchTerm);
      
      expect(highlighted).toContain('<mark>quick</mark>');
      expect(highlighted).toContain('The');
      expect(highlighted).toContain('brown fox');
    });

    it('should be case insensitive', () => {
      const text = 'The Quick Brown Fox';
      const searchTerm = 'quick';
      const highlighted = highlightText(text, searchTerm);
      
      expect(highlighted).toContain('<mark>Quick</mark>');
    });

    it('should handle multiple occurrences', () => {
      const text = 'test test test';
      const searchTerm = 'test';
      const highlighted = highlightText(text, searchTerm);
      
      const matches = highlighted.match(/<mark>test<\/mark>/g);
      expect(matches).toHaveLength(3);
    });

    it('should handle empty search term', () => {
      const text = 'Hello world';
      const highlighted = highlightText(text, '');
      
      expect(highlighted).toBe('Hello world');
    });

    it('should escape HTML in search term', () => {
      const text = 'Hello <script> world';
      const searchTerm = '<script>';
      const highlighted = highlightText(text, searchTerm);
      
      expect(highlighted).toContain('<mark>&lt;script&gt;</mark>');
    });
  });

  describe('truncateText', () => {
    it('should truncate long text', () => {
      const text = 'This is a very long text that should be truncated';
      const truncated = truncateText(text, 20);
      
      expect(truncated.length).toBeLessThanOrEqual(23); // 20 + '...'
      expect(truncated).toContain('...');
    });

    it('should not truncate short text', () => {
      const text = 'Short text';
      const truncated = truncateText(text, 20);
      
      expect(truncated).toBe('Short text');
    });

    it('should handle custom suffix', () => {
      const text = 'This is a long text';
      const truncated = truncateText(text, 10, ' [more]');
      
      expect(truncated).toContain('[more]');
    });

    it('should truncate at word boundaries', () => {
      const text = 'This is a test';
      const truncated = truncateText(text, 8, '...', true);
      
      expect(truncated).toBe('This is...');
    });
  });

  describe('capitalizeWords', () => {
    it('should capitalize first letter of each word', () => {
      const text = 'hello world test';
      const capitalized = capitalizeWords(text);
      
      expect(capitalized).toBe('Hello World Test');
    });

    it('should handle single word', () => {
      const text = 'hello';
      const capitalized = capitalizeWords(text);
      
      expect(capitalized).toBe('Hello');
    });

    it('should handle empty string', () => {
      const capitalized = capitalizeWords('');
      expect(capitalized).toBe('');
    });

    it('should handle mixed case', () => {
      const text = 'hELLo WoRLd';
      const capitalized = capitalizeWords(text);
      
      expect(capitalized).toBe('Hello World');
    });
  });

  describe('removeExtraSpaces', () => {
    it('should remove extra spaces', () => {
      const text = 'Hello    world     test';
      const cleaned = removeExtraSpaces(text);
      
      expect(cleaned).toBe('Hello world test');
    });

    it('should trim leading and trailing spaces', () => {
      const text = '   Hello world   ';
      const cleaned = removeExtraSpaces(text);
      
      expect(cleaned).toBe('Hello world');
    });

    it('should handle tabs and newlines', () => {
      const text = 'Hello\t\tworld\n\ntest';
      const cleaned = removeExtraSpaces(text);
      
      expect(cleaned).toBe('Hello world test');
    });

    it('should handle empty string', () => {
      const cleaned = removeExtraSpaces('');
      expect(cleaned).toBe('');
    });
  });

  describe('convertToSlug', () => {
    it('should convert text to URL-friendly slug', () => {
      const text = 'Hello World Test';
      const slug = convertToSlug(text);
      
      expect(slug).toBe('hello-world-test');
    });

    it('should handle special characters', () => {
      const text = 'Hello, World! & Test?';
      const slug = convertToSlug(text);
      
      expect(slug).toBe('hello-world-test');
    });

    it('should handle unicode characters', () => {
      const text = 'Café & Naïve';
      const slug = convertToSlug(text);
      
      expect(slug).toBe('cafe-naive');
    });

    it('should handle multiple spaces and dashes', () => {
      const text = 'Hello   World---Test';
      const slug = convertToSlug(text);
      
      expect(slug).toBe('hello-world-test');
    });

    it('should handle empty string', () => {
      const slug = convertToSlug('');
      expect(slug).toBe('');
    });
  });
});
