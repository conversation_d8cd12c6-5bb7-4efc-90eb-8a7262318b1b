/**
 * Test utilities for React components
 */
import React from 'react';
import { render } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import { QueryClient, QueryClientProvider } from 'react-query';
import { createTheme } from '@mui/material/styles';

// Create a test theme
const testTheme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

// Create a test query client
const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      cacheTime: 0,
    },
    mutations: {
      retry: false,
    },
  },
});

// Custom render function that includes providers
export const renderWithProviders = (ui, options = {}) => {
  const {
    queryClient = createTestQueryClient(),
    theme = testTheme,
    ...renderOptions
  } = options;

  const Wrapper = ({ children }) => (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <ThemeProvider theme={theme}>
          {children}
        </ThemeProvider>
      </BrowserRouter>
    </QueryClientProvider>
  );

  return render(ui, { wrapper: Wrapper, ...renderOptions });
};

// Mock user data
export const mockUser = {
  id: 1,
  username: 'testuser',
  email: '<EMAIL>',
  full_name: 'Test User',
  age_group: 'adult',
  is_active: true,
};

// Mock project data
export const mockProject = {
  id: 1,
  name: 'Test Project',
  description: 'A test project for testing',
  owner_id: 1,
  is_active: true,
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-01T00:00:00Z',
};

// Mock document data
export const mockDocument = {
  id: 1,
  title: 'Test Document',
  content: 'This is test content for the document.',
  document_type: 'scene',
  project_id: 1,
  order_index: 0,
  is_active: true,
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-01T00:00:00Z',
};

// Mock API responses
export const mockApiResponses = {
  login: {
    access_token: 'mock-token',
    token_type: 'bearer',
    user: mockUser,
  },
  projects: [mockProject],
  documents: [mockDocument],
  aiAssistance: {
    suggestion: 'This is a mock AI suggestion for improving your text.',
  },
  storyPrompt: {
    prompt: 'Write a story about a brave character who discovers a hidden world.',
  },
  realtimeSuggestions: {
    suggestions: [
      'excited and ready for adventure',
      'nervous but determined',
      'curious about what lay ahead',
    ],
  },
};

// Mock localStorage
export const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

// Mock API service
export const mockApiService = {
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
};

// Helper to mock successful API calls
export const mockSuccessfulApiCall = (data) => {
  return Promise.resolve({ data });
};

// Helper to mock failed API calls
export const mockFailedApiCall = (error) => {
  return Promise.reject(error);
};

// Mock Zustand store
export const createMockStore = (initialState = {}) => {
  const store = {
    ...initialState,
    setState: jest.fn(),
    getState: jest.fn(() => store),
    subscribe: jest.fn(),
    destroy: jest.fn(),
  };
  return store;
};

// Mock auth store
export const mockAuthStore = createMockStore({
  user: null,
  token: null,
  isAuthenticated: false,
  login: jest.fn(),
  logout: jest.fn(),
  register: jest.fn(),
});

// Mock project store
export const mockProjectStore = createMockStore({
  projects: [],
  currentProject: null,
  loading: false,
  error: null,
  fetchProjects: jest.fn(),
  createProject: jest.fn(),
  updateProject: jest.fn(),
  deleteProject: jest.fn(),
  setCurrentProject: jest.fn(),
});

// Mock document store
export const mockDocumentStore = createMockStore({
  documents: [],
  currentDocument: null,
  loading: false,
  error: null,
  fetchDocuments: jest.fn(),
  createDocument: jest.fn(),
  updateDocument: jest.fn(),
  deleteDocument: jest.fn(),
  setCurrentDocument: jest.fn(),
});

// Helper to wait for async operations
export const waitFor = (callback, timeout = 1000) => {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    const check = () => {
      try {
        const result = callback();
        if (result) {
          resolve(result);
        } else if (Date.now() - startTime >= timeout) {
          reject(new Error('Timeout waiting for condition'));
        } else {
          setTimeout(check, 10);
        }
      } catch (error) {
        if (Date.now() - startTime >= timeout) {
          reject(error);
        } else {
          setTimeout(check, 10);
        }
      }
    };
    check();
  });
};

// Re-export everything from testing-library
export * from '@testing-library/react';
export { renderWithProviders as render };
