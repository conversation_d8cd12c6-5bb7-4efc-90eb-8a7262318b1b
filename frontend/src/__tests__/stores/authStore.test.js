/**
 * Unit tests for auth store
 */
import { renderHook, act } from '@testing-library/react';
import { useAuthStore } from '../../stores/authStore';
import api from '../../services/api';

// Mock the API service
jest.mock('../../services/api', () => ({
  post: jest.fn(),
  get: jest.fn(),
  defaults: {
    headers: {
      common: {}
    }
  }
}));

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

describe('Auth Store', () => {
  beforeEach(() => {
    // Reset store state before each test
    useAuthStore.setState({
      user: null,
      token: null,
      isLoading: false,
      error: null,
    });
    
    jest.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  describe('Initial State', () => {
    it('should have correct initial state', () => {
      const { result } = renderHook(() => useAuthStore());

      expect(result.current.user).toBeNull();
      expect(result.current.token).toBeNull();
      expect(result.current.isLoading).toBe(false);
      expect(result.current.isAuthenticated).toBe(false);
    });
  });

  describe('Login', () => {
    it('should handle successful login', async () => {
      const mockLoginResponse = {
        data: {
          access_token: 'new-token'
        }
      };

      const mockUserResponse = {
        data: {
          id: 1,
          username: 'testuser',
          email: '<EMAIL>',
          full_name: 'Test User',
        }
      };

      api.post.mockResolvedValueOnce(mockLoginResponse);
      api.get.mockResolvedValueOnce(mockUserResponse);

      const { result } = renderHook(() => useAuthStore());

      let loginResult;
      await act(async () => {
        loginResult = await result.current.login({
          username: 'testuser',
          password: 'password',
        });
      });

      expect(loginResult.success).toBe(true);
      expect(result.current.user).toEqual(mockUserResponse.data);
      expect(result.current.token).toBe('new-token');
      expect(result.current.isLoading).toBe(false);
      expect(result.current.isAuthenticated).toBe(true);
    });

    it('should handle login failure', async () => {
      const mockError = {
        response: {
          data: {
            detail: 'Invalid credentials'
          }
        }
      };
      api.post.mockRejectedValue(mockError);

      const { result } = renderHook(() => useAuthStore());

      let loginResult;
      await act(async () => {
        loginResult = await result.current.login({
          username: 'testuser',
          password: 'wrongpassword',
        });
      });

      expect(loginResult.success).toBe(false);
      expect(loginResult.error).toBe('Invalid credentials');
      expect(result.current.user).toBeNull();
      expect(result.current.token).toBeNull();
      expect(result.current.isLoading).toBe(false);
      expect(result.current.isAuthenticated).toBe(false);
    });

  });

  describe('Logout', () => {
    it('should clear user data on logout', () => {
      const { result } = renderHook(() => useAuthStore());

      // Set initial authenticated state
      act(() => {
        result.current.setUser({ id: 1, username: 'testuser' });
        result.current.setToken('test-token');
      });

      act(() => {
        result.current.logout();
      });

      expect(result.current.user).toBeNull();
      expect(result.current.token).toBeNull();
      expect(result.current.isAuthenticated).toBe(false);
    });
  });

  describe('Authentication Status', () => {
    it('should return true for isAuthenticated when user and token exist', () => {
      const { result } = renderHook(() => useAuthStore());

      act(() => {
        result.current.setUser({ id: 1, username: 'testuser' });
        result.current.setToken('test-token');
      });

      expect(result.current.isAuthenticated).toBe(true);
    });

    it('should return false for isAuthenticated when user is null', () => {
      const { result } = renderHook(() => useAuthStore());

      act(() => {
        result.current.setUser(null);
        result.current.setToken('test-token');
      });

      expect(result.current.isAuthenticated).toBe(false);
    });

    it('should return false for isAuthenticated when token is null', () => {
      const { result } = renderHook(() => useAuthStore());

      act(() => {
        result.current.setUser({ id: 1, username: 'testuser' });
        result.current.setToken(null);
      });

      expect(result.current.isAuthenticated).toBe(false);
    });
  });
});
