/**
 * Unit tests for auth store
 */
import { renderHook, act } from '@testing-library/react';
import { useAuthStore } from '../../stores/authStore';
import * as api from '../../services/api';

// Mock the API service
jest.mock('../../services/api');

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

describe('Auth Store', () => {
  beforeEach(() => {
    // Reset store state before each test
    useAuthStore.setState({
      user: null,
      token: null,
      isLoading: false,
      error: null,
    });
    
    jest.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  describe('Initial State', () => {
    it('should have correct initial state', () => {
      const { result } = renderHook(() => useAuthStore());

      expect(result.current.user).toBeNull();
      expect(result.current.token).toBeNull();
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeNull();
      expect(result.current.isAuthenticated).toBe(false);
    });

    it('should load token from localStorage on initialization', () => {
      localStorageMock.getItem.mockReturnValue('stored-token');
      
      const { result } = renderHook(() => useAuthStore());

      expect(localStorageMock.getItem).toHaveBeenCalledWith('auth_token');
      expect(result.current.token).toBe('stored-token');
    });
  });

  describe('Login', () => {
    it('should handle successful login', async () => {
      const mockLoginResponse = {
        access_token: 'new-token',
        user: {
          id: 1,
          username: 'testuser',
          email: '<EMAIL>',
          full_name: 'Test User',
        },
      };

      api.login.mockResolvedValue(mockLoginResponse);

      const { result } = renderHook(() => useAuthStore());

      await act(async () => {
        await result.current.login({
          username: 'testuser',
          password: 'password',
        });
      });

      expect(result.current.user).toEqual(mockLoginResponse.user);
      expect(result.current.token).toBe('new-token');
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeNull();
      expect(result.current.isAuthenticated).toBe(true);
      expect(localStorageMock.setItem).toHaveBeenCalledWith('auth_token', 'new-token');
    });

    it('should handle login failure', async () => {
      const mockError = new Error('Invalid credentials');
      api.login.mockRejectedValue(mockError);

      const { result } = renderHook(() => useAuthStore());

      await act(async () => {
        await result.current.login({
          username: 'testuser',
          password: 'wrongpassword',
        });
      });

      expect(result.current.user).toBeNull();
      expect(result.current.token).toBeNull();
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe('Invalid credentials');
      expect(result.current.isAuthenticated).toBe(false);
    });

    it('should set loading state during login', async () => {
      let resolveLogin;
      const loginPromise = new Promise((resolve) => {
        resolveLogin = resolve;
      });
      
      api.login.mockReturnValue(loginPromise);

      const { result } = renderHook(() => useAuthStore());

      act(() => {
        result.current.login({
          username: 'testuser',
          password: 'password',
        });
      });

      expect(result.current.isLoading).toBe(true);

      await act(async () => {
        resolveLogin({
          access_token: 'token',
          user: { id: 1, username: 'testuser' },
        });
        await loginPromise;
      });

      expect(result.current.isLoading).toBe(false);
    });
  });

  describe('Register', () => {
    it('should handle successful registration', async () => {
      const mockRegisterResponse = {
        id: 1,
        username: 'newuser',
        email: '<EMAIL>',
        full_name: 'New User',
      };

      api.register.mockResolvedValue(mockRegisterResponse);

      const { result } = renderHook(() => useAuthStore());

      await act(async () => {
        await result.current.register({
          username: 'newuser',
          email: '<EMAIL>',
          password: 'password',
          full_name: 'New User',
        });
      });

      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeNull();
      // Registration doesn't automatically log in
      expect(result.current.isAuthenticated).toBe(false);
    });

    it('should handle registration failure', async () => {
      const mockError = new Error('Username already exists');
      api.register.mockRejectedValue(mockError);

      const { result } = renderHook(() => useAuthStore());

      await act(async () => {
        await result.current.register({
          username: 'existinguser',
          email: '<EMAIL>',
          password: 'password',
        });
      });

      expect(result.current.error).toBe('Username already exists');
      expect(result.current.isLoading).toBe(false);
    });
  });

  describe('Logout', () => {
    it('should clear user data on logout', () => {
      const { result } = renderHook(() => useAuthStore());

      // Set initial authenticated state
      act(() => {
        useAuthStore.setState({
          user: { id: 1, username: 'testuser' },
          token: 'test-token',
          isAuthenticated: true,
        });
      });

      act(() => {
        result.current.logout();
      });

      expect(result.current.user).toBeNull();
      expect(result.current.token).toBeNull();
      expect(result.current.isAuthenticated).toBe(false);
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_token');
    });
  });

  describe('Token Management', () => {
    it('should update token correctly', () => {
      const { result } = renderHook(() => useAuthStore());

      act(() => {
        result.current.setToken('new-token');
      });

      expect(result.current.token).toBe('new-token');
      expect(localStorageMock.setItem).toHaveBeenCalledWith('auth_token', 'new-token');
    });

    it('should clear token correctly', () => {
      const { result } = renderHook(() => useAuthStore());

      // Set initial token
      act(() => {
        result.current.setToken('test-token');
      });

      act(() => {
        result.current.clearToken();
      });

      expect(result.current.token).toBeNull();
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_token');
    });
  });

  describe('Error Management', () => {
    it('should set error correctly', () => {
      const { result } = renderHook(() => useAuthStore());

      act(() => {
        result.current.setError('Test error message');
      });

      expect(result.current.error).toBe('Test error message');
    });

    it('should clear error correctly', () => {
      const { result } = renderHook(() => useAuthStore());

      // Set initial error
      act(() => {
        result.current.setError('Test error');
      });

      act(() => {
        result.current.clearError();
      });

      expect(result.current.error).toBeNull();
    });
  });

  describe('User Profile Updates', () => {
    it('should update user profile', async () => {
      const mockUpdatedUser = {
        id: 1,
        username: 'testuser',
        email: '<EMAIL>',
        full_name: 'Updated Name',
      };

      api.updateProfile.mockResolvedValue(mockUpdatedUser);

      const { result } = renderHook(() => useAuthStore());

      // Set initial user
      act(() => {
        useAuthStore.setState({
          user: { id: 1, username: 'testuser', email: '<EMAIL>' },
          token: 'test-token',
        });
      });

      await act(async () => {
        await result.current.updateProfile({
          email: '<EMAIL>',
          full_name: 'Updated Name',
        });
      });

      expect(result.current.user).toEqual(mockUpdatedUser);
      expect(result.current.error).toBeNull();
    });

    it('should handle profile update failure', async () => {
      const mockError = new Error('Update failed');
      api.updateProfile.mockRejectedValue(mockError);

      const { result } = renderHook(() => useAuthStore());

      // Set initial user
      act(() => {
        useAuthStore.setState({
          user: { id: 1, username: 'testuser' },
          token: 'test-token',
        });
      });

      await act(async () => {
        await result.current.updateProfile({
          email: '<EMAIL>',
        });
      });

      expect(result.current.error).toBe('Update failed');
    });
  });

  describe('Authentication Status', () => {
    it('should return true for isAuthenticated when user and token exist', () => {
      const { result } = renderHook(() => useAuthStore());

      act(() => {
        useAuthStore.setState({
          user: { id: 1, username: 'testuser' },
          token: 'test-token',
        });
      });

      expect(result.current.isAuthenticated).toBe(true);
    });

    it('should return false for isAuthenticated when user is null', () => {
      const { result } = renderHook(() => useAuthStore());

      act(() => {
        useAuthStore.setState({
          user: null,
          token: 'test-token',
        });
      });

      expect(result.current.isAuthenticated).toBe(false);
    });

    it('should return false for isAuthenticated when token is null', () => {
      const { result } = renderHook(() => useAuthStore());

      act(() => {
        useAuthStore.setState({
          user: { id: 1, username: 'testuser' },
          token: null,
        });
      });

      expect(result.current.isAuthenticated).toBe(false);
    });
  });

  describe('Persistence', () => {
    it('should persist authentication state across page reloads', () => {
      localStorageMock.getItem.mockReturnValue('persisted-token');

      const { result } = renderHook(() => useAuthStore());

      // Simulate initialization with persisted token
      act(() => {
        result.current.initializeAuth();
      });

      expect(result.current.token).toBe('persisted-token');
    });

    it('should handle corrupted localStorage data gracefully', () => {
      localStorageMock.getItem.mockImplementation(() => {
        throw new Error('localStorage error');
      });

      const { result } = renderHook(() => useAuthStore());

      // Should not crash and should have default state
      expect(result.current.token).toBeNull();
      expect(result.current.user).toBeNull();
    });
  });
});
