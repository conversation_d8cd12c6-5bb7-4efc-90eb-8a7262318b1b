/**
 * Tests for authentication store
 */
import { renderHook, act } from '@testing-library/react';
import { useAuthStore } from '../../stores/authStore';
import api from '../../services/api';
import { mockApiResponses, mockSuccessfulApiCall, mockFailedApiCall } from '../testUtils';

// Mock the API service
jest.mock('../../services/api');
const mockedApi = api;

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

describe('Auth Store', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
    
    // Reset store state
    useAuthStore.getState().logout();
  });

  describe('Initial State', () => {
    test('has correct initial state', () => {
      const { result } = renderHook(() => useAuthStore());
      
      expect(result.current.user).toBeNull();
      expect(result.current.token).toBeNull();
      expect(result.current.isAuthenticated).toBe(false);
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBeNull();
    });

    test('loads user from localStorage on initialization', () => {
      const storedUser = JSON.stringify({
        user: { username: 'testuser' },
        token: 'stored-token',
      });
      mockLocalStorage.getItem.mockReturnValue(storedUser);
      
      // Re-initialize store
      const { result } = renderHook(() => useAuthStore());
      
      expect(result.current.user).toEqual({ username: 'testuser' });
      expect(result.current.token).toBe('stored-token');
      expect(result.current.isAuthenticated).toBe(true);
    });
  });

  describe('Login', () => {
    test('successful login updates state and stores data', async () => {
      mockedApi.post.mockResolvedValue({
        data: mockApiResponses.login,
      });

      const { result } = renderHook(() => useAuthStore());

      await act(async () => {
        await result.current.login('testuser', 'password123');
      });

      expect(result.current.user).toEqual(mockApiResponses.login.user);
      expect(result.current.token).toBe(mockApiResponses.login.access_token);
      expect(result.current.isAuthenticated).toBe(true);
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBeNull();

      // Check localStorage was called
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'auth-storage',
        expect.stringContaining('testuser')
      );
    });

    test('failed login sets error state', async () => {
      const errorResponse = {
        response: {
          data: { detail: 'Invalid credentials' },
          status: 401,
        },
      };
      mockedApi.post.mockRejectedValue(errorResponse);

      const { result } = renderHook(() => useAuthStore());

      await act(async () => {
        await result.current.login('testuser', 'wrongpassword');
      });

      expect(result.current.user).toBeNull();
      expect(result.current.token).toBeNull();
      expect(result.current.isAuthenticated).toBe(false);
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe('Invalid credentials');
    });

    test('login sets loading state during request', async () => {
      let resolvePromise;
      const promise = new Promise((resolve) => {
        resolvePromise = resolve;
      });
      mockedApi.post.mockReturnValue(promise);

      const { result } = renderHook(() => useAuthStore());

      act(() => {
        result.current.login('testuser', 'password123');
      });

      expect(result.current.loading).toBe(true);

      await act(async () => {
        resolvePromise({ data: mockApiResponses.login });
      });

      expect(result.current.loading).toBe(false);
    });
  });

  describe('Register', () => {
    test('successful registration updates state', async () => {
      const registerData = {
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123',
        full_name: 'New User',
      };

      mockedApi.post.mockResolvedValue({
        data: mockApiResponses.login,
      });

      const { result } = renderHook(() => useAuthStore());

      await act(async () => {
        await result.current.register(registerData);
      });

      expect(result.current.user).toEqual(mockApiResponses.login.user);
      expect(result.current.isAuthenticated).toBe(true);
      expect(result.current.error).toBeNull();
    });

    test('failed registration sets error state', async () => {
      const errorResponse = {
        response: {
          data: { detail: 'Username already exists' },
          status: 400,
        },
      };
      mockedApi.post.mockRejectedValue(errorResponse);

      const { result } = renderHook(() => useAuthStore());

      await act(async () => {
        await result.current.register({
          username: 'existinguser',
          email: '<EMAIL>',
          password: 'password123',
          full_name: 'Test User',
        });
      });

      expect(result.current.user).toBeNull();
      expect(result.current.isAuthenticated).toBe(false);
      expect(result.current.error).toBe('Username already exists');
    });
  });

  describe('Logout', () => {
    test('logout clears state and localStorage', () => {
      const { result } = renderHook(() => useAuthStore());

      // Set some initial state
      act(() => {
        result.current.setUser(mockApiResponses.login.user);
        result.current.setToken(mockApiResponses.login.access_token);
      });

      expect(result.current.isAuthenticated).toBe(true);

      act(() => {
        result.current.logout();
      });

      expect(result.current.user).toBeNull();
      expect(result.current.token).toBeNull();
      expect(result.current.isAuthenticated).toBe(false);
      expect(result.current.error).toBeNull();
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('auth-storage');
    });
  });

  describe('Token Management', () => {
    test('setToken updates token and localStorage', () => {
      const { result } = renderHook(() => useAuthStore());

      act(() => {
        result.current.setToken('new-token');
      });

      expect(result.current.token).toBe('new-token');
      expect(mockLocalStorage.setItem).toHaveBeenCalled();
    });

    test('clearError clears error state', () => {
      const { result } = renderHook(() => useAuthStore());

      // Set error
      act(() => {
        result.current.setError('Some error');
      });

      expect(result.current.error).toBe('Some error');

      act(() => {
        result.current.clearError();
      });

      expect(result.current.error).toBeNull();
    });
  });

  describe('Persistence', () => {
    test('persists auth state to localStorage', () => {
      const { result } = renderHook(() => useAuthStore());

      act(() => {
        result.current.setUser(mockApiResponses.login.user);
        result.current.setToken(mockApiResponses.login.access_token);
      });

      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'auth-storage',
        expect.stringContaining(mockApiResponses.login.user.username)
      );
    });

    test('handles corrupted localStorage data gracefully', () => {
      mockLocalStorage.getItem.mockReturnValue('invalid-json');

      // Should not throw error
      const { result } = renderHook(() => useAuthStore());

      expect(result.current.user).toBeNull();
      expect(result.current.token).toBeNull();
      expect(result.current.isAuthenticated).toBe(false);
    });
  });

  describe('Error Handling', () => {
    test('handles network errors', async () => {
      const networkError = new Error('Network Error');
      mockedApi.post.mockRejectedValue(networkError);

      const { result } = renderHook(() => useAuthStore());

      await act(async () => {
        await result.current.login('testuser', 'password123');
      });

      expect(result.current.error).toBe('Network error. Please try again.');
    });

    test('handles server errors', async () => {
      const serverError = {
        response: {
          status: 500,
          data: { detail: 'Internal server error' },
        },
      };
      mockedApi.post.mockRejectedValue(serverError);

      const { result } = renderHook(() => useAuthStore());

      await act(async () => {
        await result.current.login('testuser', 'password123');
      });

      expect(result.current.error).toBe('Server error. Please try again later.');
    });

    test('handles validation errors', async () => {
      const validationError = {
        response: {
          status: 422,
          data: {
            detail: [
              { msg: 'Username is required', type: 'value_error' },
            ],
          },
        },
      };
      mockedApi.post.mockRejectedValue(validationError);

      const { result } = renderHook(() => useAuthStore());

      await act(async () => {
        await result.current.login('', 'password123');
      });

      expect(result.current.error).toContain('Username is required');
    });
  });
});
