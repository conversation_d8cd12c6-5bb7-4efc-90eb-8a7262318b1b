/**
 * Unit tests for AIAssistant component
 */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from 'react-query';
import AIAssistant from '../../../components/AI/AIAssistant';
import * as aiService from '../../../services/aiService';

// Mock the AI service
jest.mock('../../../services/aiService');

// Test wrapper component
const TestWrapper = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('AIAssistant Component', () => {
  const mockProps = {
    projectId: 1,
    documentId: 1,
    context: 'This is some context text for the AI assistant.',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders AI assistant interface correctly', () => {
    render(
      <TestWrapper>
        <AIAssistant {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText(/ai assistant/i)).toBeInTheDocument();
    expect(screen.getByPlaceholderText(/ask me anything/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /send/i })).toBeInTheDocument();
  });

  it('handles user message input correctly', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <AIAssistant {...mockProps} />
      </TestWrapper>
    );

    const messageInput = screen.getByPlaceholderText(/ask me anything/i);
    await user.type(messageInput, 'Help me write a story');

    expect(messageInput).toHaveValue('Help me write a story');
  });

  it('sends message to AI service when submitted', async () => {
    const user = userEvent.setup();
    const mockChatResponse = {
      response: 'I can help you write a story! What genre are you interested in?'
    };
    
    aiService.sendChatMessage.mockResolvedValue(mockChatResponse);

    render(
      <TestWrapper>
        <AIAssistant {...mockProps} />
      </TestWrapper>
    );

    const messageInput = screen.getByPlaceholderText(/ask me anything/i);
    const sendButton = screen.getByRole('button', { name: /send/i });

    await user.type(messageInput, 'Help me write a story');
    await user.click(sendButton);

    await waitFor(() => {
      expect(aiService.sendChatMessage).toHaveBeenCalledWith({
        message: 'Help me write a story',
        projectId: 1,
        documentId: 1,
        context: 'This is some context text for the AI assistant.',
      });
    });
  });

  it('displays AI response in chat history', async () => {
    const user = userEvent.setup();
    const mockChatResponse = {
      response: 'I can help you write a story! What genre are you interested in?'
    };
    
    aiService.sendChatMessage.mockResolvedValue(mockChatResponse);

    render(
      <TestWrapper>
        <AIAssistant {...mockProps} />
      </TestWrapper>
    );

    const messageInput = screen.getByPlaceholderText(/ask me anything/i);
    const sendButton = screen.getByRole('button', { name: /send/i });

    await user.type(messageInput, 'Help me write a story');
    await user.click(sendButton);

    await waitFor(() => {
      expect(screen.getByText('Help me write a story')).toBeInTheDocument();
      expect(screen.getByText(/I can help you write a story/i)).toBeInTheDocument();
    });
  });

  it('shows loading state while waiting for AI response', async () => {
    const user = userEvent.setup();
    
    aiService.sendChatMessage.mockImplementation(
      () => new Promise(resolve => setTimeout(resolve, 1000))
    );

    render(
      <TestWrapper>
        <AIAssistant {...mockProps} />
      </TestWrapper>
    );

    const messageInput = screen.getByPlaceholderText(/ask me anything/i);
    const sendButton = screen.getByRole('button', { name: /send/i });

    await user.type(messageInput, 'Test message');
    await user.click(sendButton);

    expect(screen.getByText(/thinking/i)).toBeInTheDocument();
    expect(sendButton).toBeDisabled();
  });

  it('handles AI service errors gracefully', async () => {
    const user = userEvent.setup();
    
    aiService.sendChatMessage.mockRejectedValue(new Error('AI service unavailable'));

    render(
      <TestWrapper>
        <AIAssistant {...mockProps} />
      </TestWrapper>
    );

    const messageInput = screen.getByPlaceholderText(/ask me anything/i);
    const sendButton = screen.getByRole('button', { name: /send/i });

    await user.type(messageInput, 'Test message');
    await user.click(sendButton);

    await waitFor(() => {
      expect(screen.getByText(/sorry, something went wrong/i)).toBeInTheDocument();
    });
  });

  it('clears input after sending message', async () => {
    const user = userEvent.setup();
    const mockChatResponse = { response: 'AI response' };
    
    aiService.sendChatMessage.mockResolvedValue(mockChatResponse);

    render(
      <TestWrapper>
        <AIAssistant {...mockProps} />
      </TestWrapper>
    );

    const messageInput = screen.getByPlaceholderText(/ask me anything/i);
    const sendButton = screen.getByRole('button', { name: /send/i });

    await user.type(messageInput, 'Test message');
    await user.click(sendButton);

    await waitFor(() => {
      expect(messageInput).toHaveValue('');
    });
  });

  it('prevents sending empty messages', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <AIAssistant {...mockProps} />
      </TestWrapper>
    );

    const sendButton = screen.getByRole('button', { name: /send/i });
    await user.click(sendButton);

    expect(aiService.sendChatMessage).not.toHaveBeenCalled();
  });

  it('supports keyboard shortcuts for sending messages', async () => {
    const user = userEvent.setup();
    const mockChatResponse = { response: 'AI response' };
    
    aiService.sendChatMessage.mockResolvedValue(mockChatResponse);

    render(
      <TestWrapper>
        <AIAssistant {...mockProps} />
      </TestWrapper>
    );

    const messageInput = screen.getByPlaceholderText(/ask me anything/i);

    await user.type(messageInput, 'Test message');
    await user.keyboard('{Enter}');

    await waitFor(() => {
      expect(aiService.sendChatMessage).toHaveBeenCalled();
    });
  });

  it('displays writing assistance suggestions', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <AIAssistant {...mockProps} />
      </TestWrapper>
    );

    // Check for quick action buttons
    expect(screen.getByText(/improve text/i)).toBeInTheDocument();
    expect(screen.getByText(/continue story/i)).toBeInTheDocument();
    expect(screen.getByText(/fix grammar/i)).toBeInTheDocument();
  });

  it('handles quick action buttons correctly', async () => {
    const user = userEvent.setup();
    const mockAssistanceResponse = { response: 'Improved text here' };
    
    aiService.getWritingAssistance.mockResolvedValue(mockAssistanceResponse);

    render(
      <TestWrapper>
        <AIAssistant {...mockProps} />
      </TestWrapper>
    );

    const improveButton = screen.getByText(/improve text/i);
    await user.click(improveButton);

    await waitFor(() => {
      expect(aiService.getWritingAssistance).toHaveBeenCalledWith({
        text: 'This is some context text for the AI assistant.',
        assistanceType: 'improve',
      });
    });
  });

  it('maintains chat history across multiple messages', async () => {
    const user = userEvent.setup();
    
    aiService.sendChatMessage
      .mockResolvedValueOnce({ response: 'First response' })
      .mockResolvedValueOnce({ response: 'Second response' });

    render(
      <TestWrapper>
        <AIAssistant {...mockProps} />
      </TestWrapper>
    );

    const messageInput = screen.getByPlaceholderText(/ask me anything/i);
    const sendButton = screen.getByRole('button', { name: /send/i });

    // Send first message
    await user.type(messageInput, 'First message');
    await user.click(sendButton);

    await waitFor(() => {
      expect(screen.getByText('First message')).toBeInTheDocument();
      expect(screen.getByText('First response')).toBeInTheDocument();
    });

    // Send second message
    await user.type(messageInput, 'Second message');
    await user.click(sendButton);

    await waitFor(() => {
      expect(screen.getByText('Second message')).toBeInTheDocument();
      expect(screen.getByText('Second response')).toBeInTheDocument();
      // First message should still be visible
      expect(screen.getByText('First message')).toBeInTheDocument();
      expect(screen.getByText('First response')).toBeInTheDocument();
    });
  });

  it('scrolls to bottom when new messages are added', async () => {
    const user = userEvent.setup();
    const mockScrollIntoView = jest.fn();
    
    // Mock scrollIntoView
    Element.prototype.scrollIntoView = mockScrollIntoView;
    
    aiService.sendChatMessage.mockResolvedValue({ response: 'AI response' });

    render(
      <TestWrapper>
        <AIAssistant {...mockProps} />
      </TestWrapper>
    );

    const messageInput = screen.getByPlaceholderText(/ask me anything/i);
    const sendButton = screen.getByRole('button', { name: /send/i });

    await user.type(messageInput, 'Test message');
    await user.click(sendButton);

    await waitFor(() => {
      expect(mockScrollIntoView).toHaveBeenCalled();
    });
  });

  it('handles context updates correctly', () => {
    const { rerender } = render(
      <TestWrapper>
        <AIAssistant {...mockProps} />
      </TestWrapper>
    );

    // Update context
    rerender(
      <TestWrapper>
        <AIAssistant {...mockProps} context="Updated context text" />
      </TestWrapper>
    );

    // Component should handle context update without errors
    expect(screen.getByText(/ai assistant/i)).toBeInTheDocument();
  });
});
