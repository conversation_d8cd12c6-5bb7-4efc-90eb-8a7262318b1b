/**
 * Tests for authentication components
 */
import React from 'react';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithProviders, mockApiResponses, mockSuccessfulApiCall, mockFailedApiCall } from '../testUtils';
import Login from '../../pages/Auth/Login';
import Register from '../../pages/Auth/Register';

// Mock the API service
jest.mock('../../services/api', () => ({
  post: jest.fn(),
}));

// Mock the auth store
jest.mock('../../stores/authStore', () => ({
  useAuthStore: () => ({
    login: jest.fn(),
    register: jest.fn(),
    loading: false,
    error: null,
  }),
}));

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

describe('Login Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders login form', () => {
    renderWithProviders(<Login />);
    
    expect(screen.getByLabelText(/username/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
  });

  test('displays validation errors for empty fields', async () => {
    const user = userEvent.setup();
    renderWithProviders(<Login />);
    
    const submitButton = screen.getByRole('button', { name: /sign in/i });
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText(/username is required/i)).toBeInTheDocument();
      expect(screen.getByText(/password is required/i)).toBeInTheDocument();
    });
  });

  test('submits form with valid data', async () => {
    const user = userEvent.setup();
    const mockLogin = jest.fn();
    
    // Mock the auth store
    jest.doMock('../../stores/authStore', () => ({
      useAuthStore: () => ({
        login: mockLogin,
        loading: false,
        error: null,
      }),
    }));
    
    renderWithProviders(<Login />);
    
    const usernameInput = screen.getByLabelText(/username/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });
    
    await user.type(usernameInput, 'testuser');
    await user.type(passwordInput, 'password123');
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith('testuser', 'password123');
    });
  });

  test('displays error message on login failure', async () => {
    const user = userEvent.setup();
    
    // Mock failed login
    jest.doMock('../../stores/authStore', () => ({
      useAuthStore: () => ({
        login: jest.fn(),
        loading: false,
        error: 'Invalid credentials',
      }),
    }));
    
    renderWithProviders(<Login />);
    
    // Error should be displayed
    expect(screen.getByText(/invalid credentials/i)).toBeInTheDocument();
  });

  test('shows loading state during login', () => {
    // Mock loading state
    jest.doMock('../../stores/authStore', () => ({
      useAuthStore: () => ({
        login: jest.fn(),
        loading: true,
        error: null,
      }),
    }));
    
    renderWithProviders(<Login />);
    
    expect(screen.getByRole('button', { name: /signing in/i })).toBeDisabled();
  });

  test('navigates to register page', async () => {
    const user = userEvent.setup();
    renderWithProviders(<Login />);
    
    const registerLink = screen.getByText(/don't have an account/i);
    await user.click(registerLink);
    
    // This would depend on your actual implementation
    // expect(mockNavigate).toHaveBeenCalledWith('/register');
  });
});

describe('Register Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders registration form', () => {
    renderWithProviders(<Register />);
    
    expect(screen.getByLabelText(/username/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/full name/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /sign up/i })).toBeInTheDocument();
  });

  test('validates email format', async () => {
    const user = userEvent.setup();
    renderWithProviders(<Register />);
    
    const emailInput = screen.getByLabelText(/email/i);
    const submitButton = screen.getByRole('button', { name: /sign up/i });
    
    await user.type(emailInput, 'invalid-email');
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText(/invalid email format/i)).toBeInTheDocument();
    });
  });

  test('validates password strength', async () => {
    const user = userEvent.setup();
    renderWithProviders(<Register />);
    
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign up/i });
    
    await user.type(passwordInput, '123'); // Weak password
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText(/password must be at least/i)).toBeInTheDocument();
    });
  });

  test('submits registration form with valid data', async () => {
    const user = userEvent.setup();
    const mockRegister = jest.fn();
    
    // Mock the auth store
    jest.doMock('../../stores/authStore', () => ({
      useAuthStore: () => ({
        register: mockRegister,
        loading: false,
        error: null,
      }),
    }));
    
    renderWithProviders(<Register />);
    
    const usernameInput = screen.getByLabelText(/username/i);
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const fullNameInput = screen.getByLabelText(/full name/i);
    const submitButton = screen.getByRole('button', { name: /sign up/i });
    
    await user.type(usernameInput, 'newuser');
    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'password123');
    await user.type(fullNameInput, 'New User');
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(mockRegister).toHaveBeenCalledWith({
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123',
        full_name: 'New User',
      });
    });
  });

  test('displays error message on registration failure', () => {
    // Mock failed registration
    jest.doMock('../../stores/authStore', () => ({
      useAuthStore: () => ({
        register: jest.fn(),
        loading: false,
        error: 'Username already exists',
      }),
    }));
    
    renderWithProviders(<Register />);
    
    expect(screen.getByText(/username already exists/i)).toBeInTheDocument();
  });

  test('shows loading state during registration', () => {
    // Mock loading state
    jest.doMock('../../stores/authStore', () => ({
      useAuthStore: () => ({
        register: jest.fn(),
        loading: true,
        error: null,
      }),
    }));
    
    renderWithProviders(<Register />);
    
    expect(screen.getByRole('button', { name: /signing up/i })).toBeDisabled();
  });

  test('navigates to login page', async () => {
    const user = userEvent.setup();
    renderWithProviders(<Register />);
    
    const loginLink = screen.getByText(/already have an account/i);
    await user.click(loginLink);
    
    // This would depend on your actual implementation
    // expect(mockNavigate).toHaveBeenCalledWith('/login');
  });
});

describe('Authentication Flow', () => {
  test('redirects authenticated user from login page', () => {
    // Mock authenticated state
    jest.doMock('../../stores/authStore', () => ({
      useAuthStore: () => ({
        user: { username: 'testuser' },
        isAuthenticated: true,
        login: jest.fn(),
        loading: false,
        error: null,
      }),
    }));
    
    renderWithProviders(<Login />);
    
    // Should redirect to dashboard
    // This test would need to be implemented based on your routing logic
  });

  test('allows guest access to guest mode', () => {
    // Test guest mode functionality
    // This would depend on your guest mode implementation
  });
});
