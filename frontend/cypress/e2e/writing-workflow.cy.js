/**
 * End-to-end tests for complete writing workflow
 */

describe('Writing Workflow', () => {
  beforeEach(() => {
    cy.login('admin', 'admin123');
  });

  describe('Project Creation and Management', () => {
    it('should create a new writing project', () => {
      cy.visit('/dashboard');

      // Create new project
      cy.get('[data-testid="create-project-button"]').click();

      cy.get('[data-testid="project-name-input"]').type('My Epic Fantasy Novel');
      cy.get('[data-testid="project-description-input"]').type('A story about dragons and magic');
      cy.get('[data-testid="project-genre-select"]').select('Fantasy');

      cy.get('[data-testid="create-project-submit"]').click();

      // Should redirect to project page
      cy.url().should('include', '/projects/');
      cy.contains('My Epic Fantasy Novel').should('be.visible');
    });

    it('should navigate through project structure', () => {
      cy.createProject('Test Project', 'Test Description');

      // Should see project overview
      cy.contains('Test Project').should('be.visible');
      cy.get('[data-testid="project-documents"]').should('be.visible');
      cy.get('[data-testid="project-compendium"]').should('be.visible');

      // Navigate to documents
      cy.get('[data-testid="documents-tab"]').click();
      cy.url().should('include', '/documents');

      // Navigate to compendium
      cy.get('[data-testid="compendium-tab"]').click();
      cy.url().should('include', '/compendium');
    });
  });

  describe('Document Creation and Editing', () => {
    beforeEach(() => {
      cy.createProject('Writing Test Project', 'For testing writing features');
    });

    it('should create and edit a document', () => {
      // Create new document
      cy.get('[data-testid="create-document-button"]').click();

      cy.get('[data-testid="document-title-input"]').type('Chapter 1: The Beginning');
      cy.get('[data-testid="document-type-select"]').select('Chapter');

      cy.get('[data-testid="create-document-submit"]').click();

      // Should open document editor
      cy.url().should('include', '/documents/');
      cy.get('[data-testid="document-editor"]').should('be.visible');

      // Type content
      cy.get('[data-testid="document-editor"]')
        .type('Once upon a time, in a land far away, there lived a brave knight named Sir Galahad.');

      // Auto-save should work
      cy.contains('Saved').should('be.visible');
    });

    it('should support rich text editing', () => {
      cy.createDocument('Rich Text Test', 'Chapter');

      // Test bold formatting
      cy.get('[data-testid="document-editor"]').type('This is bold text');
      cy.get('[data-testid="document-editor"]').select();
      cy.get('[data-testid="bold-button"]').click();

      // Test italic formatting
      cy.get('[data-testid="document-editor"]').type('{enter}This is italic text');
      cy.get('[data-testid="document-editor"]').select();
      cy.get('[data-testid="italic-button"]').click();

      // Test lists
      cy.get('[data-testid="document-editor"]').type('{enter}');
      cy.get('[data-testid="bullet-list-button"]').click();
      cy.get('[data-testid="document-editor"]').type('First item{enter}Second item');

      // Verify formatting is applied
      cy.get('[data-testid="document-editor"]').should('contain.html', '<strong>');
      cy.get('[data-testid="document-editor"]').should('contain.html', '<em>');
      cy.get('[data-testid="document-editor"]').should('contain.html', '<ul>');
    });

    it('should handle document hierarchy', () => {
      // Create parent document
      cy.createDocument('Part 1: The Journey Begins', 'Part');

      // Create child document
      cy.get('[data-testid="create-child-document-button"]').click();
      cy.get('[data-testid="document-title-input"]').type('Chapter 1: Departure');
      cy.get('[data-testid="document-type-select"]').select('Chapter');
      cy.get('[data-testid="create-document-submit"]').click();

      // Should show hierarchy in sidebar
      cy.get('[data-testid="document-tree"]').should('contain', 'Part 1: The Journey Begins');
      cy.get('[data-testid="document-tree"]').should('contain', 'Chapter 1: Departure');

      // Child should be indented under parent
      cy.get('[data-testid="document-tree"] .document-child').should('contain', 'Chapter 1: Departure');
    });
  });

  describe('AI-Assisted Writing', () => {
    beforeEach(() => {
      cy.createProject('AI Writing Test', 'Testing AI features');
      cy.createDocument('AI Test Document', 'Scene');
    });

    it('should generate story prompts', () => {
      cy.get('[data-testid="ai-assistant-button"]').click();

      cy.get('[data-testid="story-prompt-button"]').click();
      cy.get('[data-testid="genre-select"]').select('Fantasy');
      cy.get('[data-testid="generate-prompt-button"]').click();

      // Should display generated prompt
      cy.get('[data-testid="generated-prompt"]').should('be.visible');
      cy.get('[data-testid="generated-prompt"]').should('not.be.empty');

      // Should be able to use prompt
      cy.get('[data-testid="use-prompt-button"]').click();
      cy.get('[data-testid="document-editor"]').should('contain', 'Write a story about');
    });

    it('should provide writing assistance', () => {
      // Add some text to improve
      cy.get('[data-testid="document-editor"]')
        .type('The cat sat on the mat. It was a nice day.');

      // Select text and get AI assistance
      cy.get('[data-testid="document-editor"]').select();
      cy.get('[data-testid="ai-improve-button"]').click();

      // Should show AI suggestions
      cy.get('[data-testid="ai-suggestions"]').should('be.visible');
      cy.get('[data-testid="ai-suggestions"]').should('not.be.empty');

      // Should be able to apply suggestion
      cy.get('[data-testid="apply-suggestion-button"]').first().click();
      cy.get('[data-testid="document-editor"]').should('not.contain', 'The cat sat on the mat');
    });

    it('should continue story with AI', () => {
      cy.get('[data-testid="document-editor"]')
        .type('The brave knight approached the dragon\'s lair, his sword gleaming in the moonlight.');

      // Position cursor at end and request continuation
      cy.get('[data-testid="document-editor"]').type('{end}');
      cy.get('[data-testid="ai-continue-button"]').click();

      // Should add AI-generated continuation
      cy.get('[data-testid="ai-continuation"]').should('be.visible');
      cy.get('[data-testid="accept-continuation-button"]').click();

      cy.get('[data-testid="document-editor"]').should('contain', 'The brave knight approached');
      cy.get('[data-testid="document-editor"]').invoke('text').should('have.length.greaterThan', 100);
    });

    it('should provide grammar and style fixes', () => {
      cy.get('[data-testid="document-editor"]')
        .type('This sentance has some erors in it and its not very good writen.');

      cy.get('[data-testid="ai-fix-button"]').click();

      // Should show corrections
      cy.get('[data-testid="grammar-fixes"]').should('be.visible');
      cy.get('[data-testid="apply-all-fixes-button"]').click();

      cy.get('[data-testid="document-editor"]').should('contain', 'sentence');
      cy.get('[data-testid="document-editor"]').should('contain', 'errors');
      cy.get('[data-testid="document-editor"]').should('contain', 'written');
    });

    it('should handle AI chat conversations', () => {
      cy.get('[data-testid="ai-chat-button"]').click();

      // Send message to AI
      cy.get('[data-testid="chat-input"]').type('Help me develop my main character');
      cy.get('[data-testid="send-chat-button"]').click();

      // Should receive AI response
      cy.get('[data-testid="chat-messages"]').should('contain', 'Help me develop my main character');
      cy.get('[data-testid="chat-messages"]').should('contain', 'I can help you develop your character');

      // Continue conversation
      cy.get('[data-testid="chat-input"]').type('The character is a young wizard');
      cy.get('[data-testid="send-chat-button"]').click();

      cy.get('[data-testid="chat-messages"]').should('contain', 'young wizard');
    });
  });

  describe('Compendium Management', () => {
    beforeEach(() => {
      cy.createProject('Compendium Test', 'Testing compendium features');
    });

    it('should create character entries', () => {
      cy.get('[data-testid="compendium-tab"]').click();

      cy.get('[data-testid="create-entry-button"]').click();
      cy.get('[data-testid="entry-type-select"]').select('Character');

      cy.get('[data-testid="entry-title-input"]').type('Sir Galahad');
      cy.get('[data-testid="entry-content-editor"]')
        .type('A brave knight of the Round Table, known for his purity and strength.');

      cy.get('[data-testid="entry-tags-input"]').type('knight{enter}hero{enter}round-table{enter}');

      cy.get('[data-testid="save-entry-button"]').click();

      // Should appear in compendium
      cy.get('[data-testid="compendium-entries"]').should('contain', 'Sir Galahad');
      cy.get('[data-testid="character-entries"]').should('contain', 'Sir Galahad');
    });

    it('should create location entries', () => {
      cy.get('[data-testid="compendium-tab"]').click();

      cy.get('[data-testid="create-entry-button"]').click();
      cy.get('[data-testid="entry-type-select"]').select('Location');

      cy.get('[data-testid="entry-title-input"]').type('Camelot');
      cy.get('[data-testid="entry-content-editor"]')
        .type('The legendary castle and court of King Arthur, seat of the Round Table.');

      cy.get('[data-testid="save-entry-button"]').click();

      cy.get('[data-testid="location-entries"]').should('contain', 'Camelot');
    });

    it('should link compendium entries to documents', () => {
      // Create character entry first
      cy.createCompendiumEntry('Merlin', 'Character', 'The wise wizard and advisor to King Arthur.');

      // Go to document editor
      cy.get('[data-testid="documents-tab"]').click();
      cy.createDocument('Story with Merlin', 'Scene');

      // Type character name and link it
      cy.get('[data-testid="document-editor"]').type('Merlin cast a powerful spell.');
      cy.get('[data-testid="document-editor"]').contains('Merlin').dblclick();

      cy.get('[data-testid="link-to-compendium-button"]').click();
      cy.get('[data-testid="compendium-search"]').type('Merlin');
      cy.get('[data-testid="compendium-result"]').contains('Merlin').click();

      // Should create link
      cy.get('[data-testid="document-editor"]').should('contain.html', '<a');
    });
  });

  describe('Collaboration Features', () => {
    it('should share project with other users', () => {
      cy.createProject('Shared Project', 'A project to share');

      cy.get('[data-testid="project-settings-button"]').click();
      cy.get('[data-testid="sharing-tab"]').click();

      cy.get('[data-testid="invite-user-input"]').type('<EMAIL>');
      cy.get('[data-testid="permission-select"]').select('Editor');
      cy.get('[data-testid="send-invite-button"]').click();

      cy.contains('Invitation sent').should('be.visible');
      cy.get('[data-testid="collaborators-list"]').should('contain', '<EMAIL>');
    });

    it('should handle real-time collaboration', () => {
      cy.createProject('Realtime Test', 'Testing real-time features');
      cy.createDocument('Collaborative Document', 'Scene');

      // Simulate another user editing
      cy.window().then((win) => {
        win.dispatchEvent(new CustomEvent('document-update', {
          detail: {
            documentId: 1,
            content: 'Another user added this text.',
            user: '<EMAIL>'
          }
        }));
      });

      // Should show other user's changes
      cy.get('[data-testid="document-editor"]').should('contain', 'Another user added this text');
      cy.get('[data-testid="collaboration-indicator"]').should('contain', '<EMAIL>');
    });
  });

  describe('Export and Publishing', () => {
    beforeEach(() => {
      cy.createProject('Export Test', 'Testing export features');
      cy.createDocument('Complete Story', 'Story');
      cy.get('[data-testid="document-editor"]')
        .type('This is a complete story with multiple paragraphs.\n\nIt has proper formatting and structure.');
    });

    it('should export project as PDF', () => {
      cy.get('[data-testid="export-button"]').click();
      cy.get('[data-testid="export-format-select"]').select('PDF');
      cy.get('[data-testid="export-options-button"]').click();

      // Configure export options
      cy.get('[data-testid="include-title-page"]').check();
      cy.get('[data-testid="include-table-of-contents"]').check();

      cy.get('[data-testid="start-export-button"]').click();

      // Should start download
      cy.get('[data-testid="export-progress"]').should('be.visible');
      cy.get('[data-testid="download-link"]').should('be.visible');
    });

    it('should export as Word document', () => {
      cy.get('[data-testid="export-button"]').click();
      cy.get('[data-testid="export-format-select"]').select('DOCX');
      cy.get('[data-testid="start-export-button"]').click();

      cy.get('[data-testid="download-link"]').should('be.visible');
    });

    it('should publish to writing platforms', () => {
      cy.get('[data-testid="publish-button"]').click();
      cy.get('[data-testid="platform-select"]').select('Medium');

      cy.get('[data-testid="publish-title-input"]').type('My Published Story');
      cy.get('[data-testid="publish-tags-input"]').type('fiction{enter}fantasy{enter}');

      cy.get('[data-testid="publish-submit-button"]').click();

      cy.contains('Published successfully').should('be.visible');
    });
  });

  describe('Writing Statistics and Analytics', () => {
    beforeEach(() => {
      cy.createProject('Stats Test', 'Testing statistics');
      cy.createDocument('Stats Document', 'Story');
    });

    it('should track writing progress', () => {
      // Add content to track
      cy.get('[data-testid="document-editor"]')
        .type('This is a test document with exactly fifty words to test the word counting feature accurately and ensure that statistics are properly calculated and displayed to users.');

      // Check word count
      cy.get('[data-testid="word-count"]').should('contain', '50');

      // Check writing statistics
      cy.get('[data-testid="stats-button"]').click();
      cy.get('[data-testid="total-words"]').should('contain', '50');
      cy.get('[data-testid="writing-streak"]').should('be.visible');
    });

    it('should show writing goals progress', () => {
      cy.get('[data-testid="goals-button"]').click();

      // Set daily word goal
      cy.get('[data-testid="daily-goal-input"]').clear().type('500');
      cy.get('[data-testid="save-goals-button"]').click();

      // Add some words
      cy.get('[data-testid="document-editor"]')
        .type('Writing towards my daily goal. '.repeat(20));

      // Check progress
      cy.get('[data-testid="goal-progress"]').should('be.visible');
      cy.get('[data-testid="goal-percentage"]').should('not.contain', '0%');
    });
  });
});

// Custom commands for common actions
Cypress.Commands.add('createProject', (name, description) => {
  cy.visit('/dashboard');
  cy.get('[data-testid="create-project-button"]').click();
  cy.get('[data-testid="project-name-input"]').type(name);
  cy.get('[data-testid="project-description-input"]').type(description);
  cy.get('[data-testid="create-project-submit"]').click();
});

Cypress.Commands.add('createDocument', (title, type) => {
  cy.get('[data-testid="create-document-button"]').click();
  cy.get('[data-testid="document-title-input"]').type(title);
  cy.get('[data-testid="document-type-select"]').select(type);
  cy.get('[data-testid="create-document-submit"]').click();
});

Cypress.Commands.add('createCompendiumEntry', (title, type, content) => {
  cy.get('[data-testid="compendium-tab"]').click();
  cy.get('[data-testid="create-entry-button"]').click();
  cy.get('[data-testid="entry-type-select"]').select(type);
  cy.get('[data-testid="entry-title-input"]').type(title);
  cy.get('[data-testid="entry-content-editor"]').type(content);
  cy.get('[data-testid="save-entry-button"]').click();
});
