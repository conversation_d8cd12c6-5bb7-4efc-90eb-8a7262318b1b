/**
 * End-to-end tests for user authentication flows
 */

describe('User Authentication', () => {
  beforeEach(() => {
    // Clear any existing auth state
    cy.clearLocalStorage();
    cy.clearCookies();
  });

  describe('User Registration', () => {
    it('should allow new user registration', () => {
      cy.visit('/register');

      // Fill registration form
      cy.get('[data-testid="username-input"]').type('newuser');
      cy.get('[data-testid="email-input"]').type('<EMAIL>');
      cy.get('[data-testid="password-input"]').type('password123');
      cy.get('[data-testid="confirm-password-input"]').type('password123');
      cy.get('[data-testid="full-name-input"]').type('New User');

      // Submit form
      cy.get('[data-testid="register-button"]').click();

      // Should redirect to login or dashboard
      cy.url().should('not.include', '/register');
      cy.contains('Welcome').should('be.visible');
    });

    it('should show validation errors for invalid input', () => {
      cy.visit('/register');

      // Try to submit empty form
      cy.get('[data-testid="register-button"]').click();

      // Should show validation errors
      cy.contains('Username is required').should('be.visible');
      cy.contains('Email is required').should('be.visible');
      cy.contains('Password is required').should('be.visible');
    });

    it('should show error for duplicate username', () => {
      cy.visit('/register');

      // Fill form with existing username
      cy.get('[data-testid="username-input"]').type('admin');
      cy.get('[data-testid="email-input"]').type('<EMAIL>');
      cy.get('[data-testid="password-input"]').type('password123');
      cy.get('[data-testid="confirm-password-input"]').type('password123');
      cy.get('[data-testid="full-name-input"]').type('Different User');

      cy.get('[data-testid="register-button"]').click();

      // Should show error message
      cy.contains('Username already exists').should('be.visible');
    });

    it('should validate password confirmation', () => {
      cy.visit('/register');

      cy.get('[data-testid="username-input"]').type('testuser');
      cy.get('[data-testid="email-input"]').type('<EMAIL>');
      cy.get('[data-testid="password-input"]').type('password123');
      cy.get('[data-testid="confirm-password-input"]').type('differentpassword');

      cy.get('[data-testid="register-button"]').click();

      cy.contains('Passwords do not match').should('be.visible');
    });
  });

  describe('User Login', () => {
    it('should allow user login with valid credentials', () => {
      cy.visit('/login');

      // Fill login form
      cy.get('[data-testid="username-input"]').type('admin');
      cy.get('[data-testid="password-input"]').type('admin123');

      // Submit form
      cy.get('[data-testid="login-button"]').click();

      // Should redirect to dashboard
      cy.url().should('include', '/dashboard');
      cy.contains('Welcome back').should('be.visible');

      // Should store auth token
      cy.window().its('localStorage').invoke('getItem', 'auth_token').should('exist');
    });

    it('should show error for invalid credentials', () => {
      cy.visit('/login');

      cy.get('[data-testid="username-input"]').type('admin');
      cy.get('[data-testid="password-input"]').type('wrongpassword');

      cy.get('[data-testid="login-button"]').click();

      cy.contains('Invalid credentials').should('be.visible');
      cy.url().should('include', '/login');
    });

    it('should show validation errors for empty fields', () => {
      cy.visit('/login');

      cy.get('[data-testid="login-button"]').click();

      cy.contains('Username is required').should('be.visible');
      cy.contains('Password is required').should('be.visible');
    });

    it('should handle "Remember Me" functionality', () => {
      cy.visit('/login');

      cy.get('[data-testid="username-input"]').type('admin');
      cy.get('[data-testid="password-input"]').type('admin123');
      cy.get('[data-testid="remember-me-checkbox"]').check();

      cy.get('[data-testid="login-button"]').click();

      // Should store remember me preference
      cy.window().its('localStorage').invoke('getItem', 'remember_me').should('equal', 'true');
    });
  });

  describe('User Logout', () => {
    beforeEach(() => {
      // Login before each logout test
      cy.login('admin', 'admin123');
    });

    it('should allow user logout', () => {
      cy.visit('/dashboard');

      // Click user menu
      cy.get('[data-testid="user-menu-button"]').click();

      // Click logout
      cy.get('[data-testid="logout-button"]').click();

      // Should redirect to login page
      cy.url().should('include', '/login');

      // Should clear auth token
      cy.window().its('localStorage').invoke('getItem', 'auth_token').should('not.exist');
    });

    it('should logout from all tabs when logging out', () => {
      cy.visit('/dashboard');

      // Simulate logout from another tab by clearing localStorage
      cy.window().its('localStorage').invoke('removeItem', 'auth_token');

      // Trigger storage event
      cy.window().then((win) => {
        win.dispatchEvent(new StorageEvent('storage', {
          key: 'auth_token',
          oldValue: 'some-token',
          newValue: null,
        }));
      });

      // Should redirect to login
      cy.url().should('include', '/login');
    });
  });

  describe('Protected Routes', () => {
    it('should redirect unauthenticated users to login', () => {
      cy.visit('/dashboard');

      cy.url().should('include', '/login');
      cy.contains('Please sign in').should('be.visible');
    });

    it('should allow access to protected routes when authenticated', () => {
      cy.login('admin', 'admin123');

      cy.visit('/dashboard');
      cy.url().should('include', '/dashboard');

      cy.visit('/projects');
      cy.url().should('include', '/projects');
    });

    it('should redirect to intended page after login', () => {
      // Try to access protected page
      cy.visit('/projects');

      // Should redirect to login
      cy.url().should('include', '/login');

      // Login
      cy.get('[data-testid="username-input"]').type('admin');
      cy.get('[data-testid="password-input"]').type('admin123');
      cy.get('[data-testid="login-button"]').click();

      // Should redirect to originally intended page
      cy.url().should('include', '/projects');
    });
  });

  describe('Session Management', () => {
    it('should handle expired tokens gracefully', () => {
      cy.login('admin', 'admin123');
      cy.visit('/dashboard');

      // Simulate expired token by setting invalid token
      cy.window().its('localStorage').invoke('setItem', 'auth_token', 'expired-token');

      // Make a request that would trigger token validation
      cy.get('[data-testid="create-project-button"]').click();

      // Should redirect to login
      cy.url().should('include', '/login');
      cy.contains('Session expired').should('be.visible');
    });

    it('should refresh token automatically when needed', () => {
      cy.login('admin', 'admin123');
      cy.visit('/dashboard');

      // Simulate token refresh by intercepting API calls
      cy.intercept('GET', '/api/projects/', {
        statusCode: 401,
        body: { detail: 'Token expired' }
      }).as('expiredToken');

      cy.intercept('POST', '/api/auth/refresh', {
        statusCode: 200,
        body: { access_token: 'new-token' }
      }).as('refreshToken');

      // Trigger API call
      cy.reload();

      cy.wait('@expiredToken');
      cy.wait('@refreshToken');

      // Should continue working with new token
      cy.url().should('include', '/dashboard');
    });
  });

  describe('Password Reset', () => {
    it('should allow password reset request', () => {
      cy.visit('/login');

      cy.get('[data-testid="forgot-password-link"]').click();

      cy.url().should('include', '/forgot-password');

      cy.get('[data-testid="email-input"]').type('<EMAIL>');
      cy.get('[data-testid="reset-password-button"]').click();

      cy.contains('Password reset email sent').should('be.visible');
    });

    it('should handle password reset with valid token', () => {
      // Simulate clicking reset link from email
      cy.visit('/reset-password?token=valid-reset-token');

      cy.get('[data-testid="new-password-input"]').type('newpassword123');
      cy.get('[data-testid="confirm-password-input"]').type('newpassword123');

      cy.get('[data-testid="update-password-button"]').click();

      cy.contains('Password updated successfully').should('be.visible');
      cy.url().should('include', '/login');
    });

    it('should handle invalid reset tokens', () => {
      cy.visit('/reset-password?token=invalid-token');

      cy.contains('Invalid or expired reset token').should('be.visible');
    });
  });

  describe('User Profile Management', () => {
    beforeEach(() => {
      cy.login('admin', 'admin123');
    });

    it('should allow profile updates', () => {
      cy.visit('/profile');

      cy.get('[data-testid="full-name-input"]').clear().type('Updated Admin Name');
      cy.get('[data-testid="email-input"]').clear().type('<EMAIL>');

      cy.get('[data-testid="save-profile-button"]').click();

      cy.contains('Profile updated successfully').should('be.visible');
    });

    it('should allow password change', () => {
      cy.visit('/profile');

      cy.get('[data-testid="change-password-tab"]').click();

      cy.get('[data-testid="current-password-input"]').type('admin123');
      cy.get('[data-testid="new-password-input"]').type('newadminpass123');
      cy.get('[data-testid="confirm-new-password-input"]').type('newadminpass123');

      cy.get('[data-testid="change-password-button"]').click();

      cy.contains('Password changed successfully').should('be.visible');
    });
  });

  describe('Account Deletion', () => {
    it('should allow account deletion with confirmation', () => {
      cy.login('admin', 'admin123');
      cy.visit('/profile');

      cy.get('[data-testid="delete-account-tab"]').click();

      cy.get('[data-testid="delete-account-button"]').click();

      // Should show confirmation dialog
      cy.get('[data-testid="confirm-delete-dialog"]').should('be.visible');
      cy.get('[data-testid="delete-confirmation-input"]').type('DELETE');

      cy.get('[data-testid="confirm-delete-button"]').click();

      // Should redirect to goodbye page
      cy.url().should('include', '/goodbye');
      cy.contains('Account deleted successfully').should('be.visible');
    });
  });
});

// Custom command for login
Cypress.Commands.add('login', (username, password) => {
  cy.request({
    method: 'POST',
    url: '/api/auth/login',
    body: {
      username,
      password,
    },
  }).then((response) => {
    window.localStorage.setItem('auth_token', response.body.access_token);
  });
});
