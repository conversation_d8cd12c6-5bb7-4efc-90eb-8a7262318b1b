// ***********************************************************
// This example support/e2e.js is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands';

// Alternatively you can use CommonJS syntax:
// require('./commands')

// Hide fetch/XHR requests from command log
const app = window.top;
if (!app.document.head.querySelector('[data-hide-command-log-request]')) {
  const style = app.document.createElement('style');
  style.innerHTML = '.command-name-request, .command-name-xhr { display: none }';
  style.setAttribute('data-hide-command-log-request', '');
  app.document.head.appendChild(style);
}

// Global error handling
Cypress.on('uncaught:exception', (err, runnable) => {
  // Returning false here prevents Cypress from failing the test
  // on uncaught exceptions that we expect (like React dev warnings)
  if (err.message.includes('ResizeObserver loop limit exceeded')) {
    return false;
  }
  if (err.message.includes('Non-Error promise rejection captured')) {
    return false;
  }
  return true;
});

// Custom commands for common operations
beforeEach(() => {
  // Clear localStorage before each test
  cy.clearLocalStorage();
  
  // Set up API interceptors for consistent testing
  cy.intercept('GET', '/api/auth/me', { fixture: 'user.json' }).as('getCurrentUser');
  cy.intercept('POST', '/api/auth/login', { fixture: 'login.json' }).as('login');
  cy.intercept('POST', '/api/auth/register', { fixture: 'register.json' }).as('register');
  cy.intercept('GET', '/api/projects', { fixture: 'projects.json' }).as('getProjects');
  cy.intercept('POST', '/api/projects', { fixture: 'project.json' }).as('createProject');
  cy.intercept('GET', '/api/documents/*', { fixture: 'documents.json' }).as('getDocuments');
  cy.intercept('POST', '/api/ai/assist', { fixture: 'aiAssistance.json' }).as('getAIAssistance');
});

// Add custom assertions
chai.use((chai, utils) => {
  chai.Assertion.addMethod('beVisible', function () {
    const obj = this._obj;
    this.assert(
      obj.should('be.visible'),
      'expected #{this} to be visible',
      'expected #{this} not to be visible'
    );
  });
});

// Performance monitoring
let performanceMarks = [];

Cypress.Commands.add('startPerformanceMonitoring', (markName) => {
  cy.window().then((win) => {
    win.performance.mark(`${markName}-start`);
    performanceMarks.push(markName);
  });
});

Cypress.Commands.add('endPerformanceMonitoring', (markName, maxDuration = 5000) => {
  cy.window().then((win) => {
    win.performance.mark(`${markName}-end`);
    win.performance.measure(markName, `${markName}-start`, `${markName}-end`);
    
    const measure = win.performance.getEntriesByName(markName)[0];
    expect(measure.duration).to.be.lessThan(maxDuration);
    
    cy.log(`Performance: ${markName} took ${measure.duration.toFixed(2)}ms`);
  });
});
