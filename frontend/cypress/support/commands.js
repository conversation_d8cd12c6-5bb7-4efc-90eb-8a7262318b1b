// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

// Authentication commands
Cypress.Commands.add('login', (username, password) => {
  cy.session([username, password], () => {
    cy.request({
      method: 'POST',
      url: '/api/auth/login',
      body: {
        username,
        password,
      },
    }).then((response) => {
      window.localStorage.setItem('auth_token', response.body.access_token);
    });
  });
});

Cypress.Commands.add('logout', () => {
  cy.window().then((win) => {
    win.localStorage.removeItem('auth_token');
  });
});

// Project management commands
Cypress.Commands.add('createProject', (name, description, genre = 'Fantasy') => {
  cy.visit('/dashboard');
  cy.get('[data-testid="create-project-button"]').click();
  cy.get('[data-testid="project-name-input"]').type(name);
  if (description) {
    cy.get('[data-testid="project-description-input"]').type(description);
  }
  if (genre) {
    cy.get('[data-testid="project-genre-select"]').select(genre);
  }
  cy.get('[data-testid="create-project-submit"]').click();
  cy.url().should('include', '/projects/');
});

Cypress.Commands.add('deleteProject', (projectId) => {
  cy.request({
    method: 'DELETE',
    url: `/api/projects/${projectId}`,
    headers: {
      Authorization: `Bearer ${window.localStorage.getItem('auth_token')}`,
    },
  });
});

// Document management commands
Cypress.Commands.add('createDocument', (title, type = 'Scene', content = '') => {
  cy.get('[data-testid="create-document-button"]').click();
  cy.get('[data-testid="document-title-input"]').type(title);
  cy.get('[data-testid="document-type-select"]').select(type);
  cy.get('[data-testid="create-document-submit"]').click();
  
  if (content) {
    cy.get('[data-testid="document-editor"]').type(content);
    cy.wait(1000); // Wait for auto-save
  }
});

Cypress.Commands.add('openDocument', (documentTitle) => {
  cy.get('[data-testid="document-tree"]').contains(documentTitle).click();
});

// Compendium commands
Cypress.Commands.add('createCompendiumEntry', (title, type, content, tags = []) => {
  cy.get('[data-testid="compendium-tab"]').click();
  cy.get('[data-testid="create-entry-button"]').click();
  cy.get('[data-testid="entry-type-select"]').select(type);
  cy.get('[data-testid="entry-title-input"]').type(title);
  cy.get('[data-testid="entry-content-editor"]').type(content);
  
  if (tags.length > 0) {
    tags.forEach(tag => {
      cy.get('[data-testid="entry-tags-input"]').type(`${tag}{enter}`);
    });
  }
  
  cy.get('[data-testid="save-entry-button"]').click();
});

// AI assistance commands
Cypress.Commands.add('getAIAssistance', (text, assistanceType = 'improve') => {
  cy.get('[data-testid="document-editor"]').clear().type(text);
  cy.get('[data-testid="document-editor"]').select();
  
  switch (assistanceType) {
    case 'improve':
      cy.get('[data-testid="ai-improve-button"]').click();
      break;
    case 'continue':
      cy.get('[data-testid="ai-continue-button"]').click();
      break;
    case 'fix':
      cy.get('[data-testid="ai-fix-button"]').click();
      break;
  }
  
  cy.get('[data-testid="ai-suggestions"]').should('be.visible');
});

Cypress.Commands.add('chatWithAI', (message) => {
  cy.get('[data-testid="ai-chat-button"]').click();
  cy.get('[data-testid="chat-input"]').type(message);
  cy.get('[data-testid="send-chat-button"]').click();
  cy.get('[data-testid="chat-messages"]').should('contain', message);
});

// Utility commands
Cypress.Commands.add('waitForAutoSave', () => {
  cy.get('[data-testid="save-indicator"]').should('contain', 'Saved');
});

Cypress.Commands.add('checkWordCount', (expectedCount) => {
  cy.get('[data-testid="word-count"]').should('contain', expectedCount.toString());
});

Cypress.Commands.add('setWritingGoal', (dailyWords) => {
  cy.get('[data-testid="goals-button"]').click();
  cy.get('[data-testid="daily-goal-input"]').clear().type(dailyWords.toString());
  cy.get('[data-testid="save-goals-button"]').click();
});

// File upload commands
Cypress.Commands.add('uploadFile', (fileName, fileType = 'text/plain') => {
  cy.fixture(fileName).then(fileContent => {
    cy.get('[data-testid="file-upload-input"]').selectFile({
      contents: Cypress.Buffer.from(fileContent),
      fileName,
      mimeType: fileType,
    });
  });
});

// Export commands
Cypress.Commands.add('exportProject', (format = 'PDF') => {
  cy.get('[data-testid="export-button"]').click();
  cy.get('[data-testid="export-format-select"]').select(format);
  cy.get('[data-testid="start-export-button"]').click();
  cy.get('[data-testid="export-progress"]').should('be.visible');
});

// Theme and settings commands
Cypress.Commands.add('switchTheme', (theme = 'dark') => {
  cy.get('[data-testid="user-menu-button"]').click();
  cy.get('[data-testid="settings-button"]').click();
  cy.get('[data-testid="theme-select"]').select(theme);
  cy.get('[data-testid="save-settings-button"]').click();
});

// Accessibility commands
Cypress.Commands.add('checkA11y', () => {
  cy.injectAxe();
  cy.checkA11y();
});

// Performance commands
Cypress.Commands.add('measurePerformance', (actionName) => {
  cy.window().then((win) => {
    win.performance.mark(`${actionName}-start`);
  });
  
  return {
    end: () => {
      cy.window().then((win) => {
        win.performance.mark(`${actionName}-end`);
        win.performance.measure(actionName, `${actionName}-start`, `${actionName}-end`);
        
        const measure = win.performance.getEntriesByName(actionName)[0];
        cy.log(`${actionName} took ${measure.duration}ms`);
        
        // Assert performance threshold (adjust as needed)
        expect(measure.duration).to.be.lessThan(5000);
      });
    }
  };
});

// Network simulation commands
Cypress.Commands.add('simulateSlowNetwork', () => {
  cy.intercept('**', (req) => {
    req.reply((res) => {
      res.delay(2000); // 2 second delay
    });
  });
});

Cypress.Commands.add('simulateOffline', () => {
  cy.intercept('**', { forceNetworkError: true });
});

// Data cleanup commands
Cypress.Commands.add('cleanupTestData', () => {
  // Clean up any test data created during tests
  cy.request({
    method: 'DELETE',
    url: '/api/test/cleanup',
    headers: {
      Authorization: `Bearer ${window.localStorage.getItem('auth_token')}`,
    },
    failOnStatusCode: false,
  });
});

// Visual regression commands (if using visual testing)
Cypress.Commands.add('compareSnapshot', (name) => {
  cy.get('[data-testid="main-content"]').matchImageSnapshot(name);
});

// Mobile simulation commands
Cypress.Commands.add('setMobileViewport', () => {
  cy.viewport(375, 667); // iPhone SE dimensions
});

Cypress.Commands.add('setTabletViewport', () => {
  cy.viewport(768, 1024); // iPad dimensions
});

// Keyboard navigation commands
Cypress.Commands.add('navigateWithKeyboard', (direction) => {
  const keyMap = {
    up: '{uparrow}',
    down: '{downarrow}',
    left: '{leftarrow}',
    right: '{rightarrow}',
    tab: '{tab}',
    enter: '{enter}',
    escape: '{esc}',
  };
  
  cy.focused().type(keyMap[direction] || direction);
});

// Local storage commands
Cypress.Commands.add('setLocalStorage', (key, value) => {
  cy.window().then((win) => {
    win.localStorage.setItem(key, JSON.stringify(value));
  });
});

Cypress.Commands.add('getLocalStorage', (key) => {
  return cy.window().then((win) => {
    const value = win.localStorage.getItem(key);
    return value ? JSON.parse(value) : null;
  });
});

// Error handling commands
Cypress.Commands.add('expectError', (errorMessage) => {
  cy.get('[data-testid="error-message"]').should('contain', errorMessage);
});

Cypress.Commands.add('expectSuccess', (successMessage) => {
  cy.get('[data-testid="success-message"]').should('contain', successMessage);
});

// Form validation commands
Cypress.Commands.add('submitFormAndExpectValidation', (fieldName, errorMessage) => {
  cy.get('[data-testid="submit-button"]').click();
  cy.get(`[data-testid="${fieldName}-error"]`).should('contain', errorMessage);
});

// Wait for specific conditions
Cypress.Commands.add('waitForLoadingToFinish', () => {
  cy.get('[data-testid="loading-spinner"]').should('not.exist');
});

Cypress.Commands.add('waitForApiCall', (alias) => {
  cy.wait(alias).then((interception) => {
    expect(interception.response.statusCode).to.be.oneOf([200, 201, 204]);
  });
});
