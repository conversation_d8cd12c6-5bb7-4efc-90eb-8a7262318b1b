// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

// Authentication commands
Cypress.Commands.add('login', (username = 'testuser', password = 'password123') => {
  cy.visit('/login');
  cy.get('[data-testid="username-input"]').type(username);
  cy.get('[data-testid="password-input"]').type(password);
  cy.get('[data-testid="login-button"]').click();
  cy.wait('@login');
  cy.url().should('include', '/dashboard');
});

Cypress.Commands.add('loginViaAPI', (username = 'testuser', password = 'password123') => {
  cy.request({
    method: 'POST',
    url: `${Cypress.env('apiUrl')}/auth/login`,
    body: {
      username,
      password,
    },
  }).then((response) => {
    const { access_token, user } = response.body;
    
    // Store auth data in localStorage
    cy.window().then((win) => {
      win.localStorage.setItem('auth-storage', JSON.stringify({
        state: {
          user,
          token: access_token,
          isAuthenticated: true,
        },
        version: 0,
      }));
    });
  });
});

Cypress.Commands.add('logout', () => {
  cy.get('[data-testid="user-menu"]').click();
  cy.get('[data-testid="logout-button"]').click();
  cy.url().should('include', '/login');
});

// Project management commands
Cypress.Commands.add('createProject', (projectName = 'Test Project', description = 'Test Description') => {
  cy.get('[data-testid="create-project-button"]').click();
  cy.get('[data-testid="project-name-input"]').type(projectName);
  cy.get('[data-testid="project-description-input"]').type(description);
  cy.get('[data-testid="save-project-button"]').click();
  cy.wait('@createProject');
});

Cypress.Commands.add('openProject', (projectName) => {
  cy.get('[data-testid="project-card"]').contains(projectName).click();
  cy.url().should('include', '/project/');
});

// Document management commands
Cypress.Commands.add('createDocument', (title = 'Test Document', content = 'Test content') => {
  cy.get('[data-testid="create-document-button"]').click();
  cy.get('[data-testid="document-title-input"]').type(title);
  cy.get('[data-testid="document-editor"]').type(content);
  cy.get('[data-testid="save-document-button"]').click();
});

Cypress.Commands.add('openDocument', (documentTitle) => {
  cy.get('[data-testid="document-item"]').contains(documentTitle).click();
  cy.url().should('include', '/document/');
});

// AI assistance commands
Cypress.Commands.add('useAIAssistance', (text, assistanceType = 'improve') => {
  cy.get('[data-testid="ai-assistant-button"]').click();
  cy.get('[data-testid="ai-text-input"]').clear().type(text);
  cy.get('[data-testid="assistance-type-select"]').select(assistanceType);
  cy.get('[data-testid="get-assistance-button"]').click();
  cy.wait('@getAIAssistance');
});

// Form helpers
Cypress.Commands.add('fillForm', (formData) => {
  Object.keys(formData).forEach((field) => {
    cy.get(`[data-testid="${field}-input"]`).type(formData[field]);
  });
});

Cypress.Commands.add('submitForm', (formTestId = 'form') => {
  cy.get(`[data-testid="${formTestId}"]`).submit();
});

// Wait for loading states
Cypress.Commands.add('waitForLoading', () => {
  cy.get('[data-testid="loading-spinner"]').should('not.exist');
});

Cypress.Commands.add('waitForPageLoad', () => {
  cy.get('[data-testid="page-content"]').should('be.visible');
  cy.waitForLoading();
});

// Error handling
Cypress.Commands.add('checkForErrors', () => {
  cy.get('[data-testid="error-message"]').should('not.exist');
  cy.get('.MuiAlert-standardError').should('not.exist');
});

// Accessibility helpers
Cypress.Commands.add('checkA11y', () => {
  // Basic accessibility checks
  cy.get('img').each(($img) => {
    cy.wrap($img).should('have.attr', 'alt');
  });
  
  cy.get('button').each(($btn) => {
    cy.wrap($btn).should('be.visible');
  });
  
  cy.get('input').each(($input) => {
    cy.wrap($input).should('have.attr', 'aria-label').or('have.attr', 'aria-labelledby');
  });
});

// Mobile testing helpers
Cypress.Commands.add('setMobileViewport', () => {
  cy.viewport(375, 667); // iPhone SE
});

Cypress.Commands.add('setTabletViewport', () => {
  cy.viewport(768, 1024); // iPad
});

Cypress.Commands.add('setDesktopViewport', () => {
  cy.viewport(1280, 720); // Desktop
});

// Data management
Cypress.Commands.add('seedTestData', () => {
  // Create test user and projects via API
  cy.request({
    method: 'POST',
    url: `${Cypress.env('apiUrl')}/auth/register`,
    body: {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'password123',
      full_name: 'Test User',
    },
    failOnStatusCode: false,
  });
});

Cypress.Commands.add('cleanupTestData', () => {
  // Clean up test data
  cy.clearLocalStorage();
  cy.clearCookies();
});

// Screenshot helpers
Cypress.Commands.add('takeScreenshot', (name) => {
  cy.screenshot(name, { capture: 'viewport' });
});

// Network simulation
Cypress.Commands.add('simulateSlowNetwork', () => {
  cy.intercept('**', (req) => {
    req.reply((res) => {
      res.delay(2000); // 2 second delay
    });
  });
});

Cypress.Commands.add('simulateOffline', () => {
  cy.intercept('**', { forceNetworkError: true });
});

// Custom assertions
Cypress.Commands.add('shouldBeAccessible', { prevSubject: 'element' }, (subject) => {
  cy.wrap(subject).should('be.visible');
  cy.wrap(subject).should('not.have.attr', 'aria-hidden', 'true');
});

Cypress.Commands.add('shouldHaveValidHTML', () => {
  cy.document().then((doc) => {
    const html = doc.documentElement.outerHTML;
    expect(html).to.include('<!DOCTYPE html>');
    expect(html).to.include('<html');
    expect(html).to.include('<head>');
    expect(html).to.include('<body>');
  });
});
