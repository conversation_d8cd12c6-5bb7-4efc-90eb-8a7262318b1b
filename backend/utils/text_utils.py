"""
Text utility functions for processing and analyzing text
"""
import re
import html
from typing import List, Optional


def count_words(text: str) -> int:
    """
    Count the number of words in a text string.
    
    Args:
        text: The text to count words in
        
    Returns:
        int: Number of words
    """
    if not text or not text.strip():
        return 0
    
    # Remove extra whitespace and split by whitespace
    words = text.strip().split()
    return len(words)


def extract_keywords(text: str, max_keywords: int = 10) -> List[str]:
    """
    Extract keywords from text by filtering out common stopwords.
    
    Args:
        text: The text to extract keywords from
        max_keywords: Maximum number of keywords to return
        
    Returns:
        List[str]: List of keywords
    """
    if not text:
        return []
    
    # Common English stopwords
    stopwords = {
        'a', 'an', 'and', 'are', 'as', 'at', 'be', 'by', 'for', 'from',
        'has', 'he', 'in', 'is', 'it', 'its', 'of', 'on', 'that', 'the',
        'to', 'was', 'will', 'with', 'or', 'but', 'not', 'this', 'they',
        'have', 'had', 'what', 'when', 'where', 'who', 'which', 'why', 'how'
    }
    
    # Extract words and filter
    words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())
    keywords = [word for word in words if word not in stopwords]
    
    # Remove duplicates while preserving order
    unique_keywords = []
    seen = set()
    for keyword in keywords:
        if keyword not in seen:
            unique_keywords.append(keyword)
            seen.add(keyword)
    
    return unique_keywords[:max_keywords]


def sanitize_text(text: str, allow_html: bool = False) -> str:
    """
    Sanitize text by removing or escaping potentially dangerous content.
    
    Args:
        text: The text to sanitize
        allow_html: Whether to allow safe HTML tags
        
    Returns:
        str: Sanitized text
    """
    if not text:
        return ""
    
    # Remove script tags and their content
    text = re.sub(r'<script[^>]*>.*?</script>', '', text, flags=re.IGNORECASE | re.DOTALL)
    
    # Remove dangerous attributes
    text = re.sub(r'\s(on\w+|javascript:|data:)\s*=\s*["\'][^"\']*["\']', '', text, flags=re.IGNORECASE)
    
    if not allow_html:
        # Remove all HTML tags
        text = re.sub(r'<[^>]+>', '', text)
    else:
        # Allow only safe HTML tags
        safe_tags = ['p', 'br', 'strong', 'em', 'u', 'b', 'i', 'ul', 'ol', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6']
        # Remove unsafe tags (this is a simplified approach)
        text = re.sub(r'<(?!/?(?:' + '|'.join(safe_tags) + r')\b)[^>]+>', '', text, flags=re.IGNORECASE)
    
    # Escape remaining HTML entities
    text = html.escape(text, quote=False)
    
    return text.strip()


def format_text(text: str, format_type: str) -> str:
    """
    Apply formatting to text.
    
    Args:
        text: The text to format
        format_type: Type of formatting ('bold', 'italic', 'underline')
        
    Returns:
        str: Formatted text
    """
    if not text:
        return ""
    
    format_map = {
        'bold': f'<strong>{text}</strong>',
        'italic': f'<em>{text}</em>',
        'underline': f'<u>{text}</u>'
    }
    
    return format_map.get(format_type, text)


def calculate_reading_time(text: str, words_per_minute: int = 200) -> int:
    """
    Calculate estimated reading time for text.
    
    Args:
        text: The text to calculate reading time for
        words_per_minute: Average reading speed
        
    Returns:
        int: Estimated reading time in minutes
    """
    if not text:
        return 0
    
    word_count = count_words(text)
    if word_count == 0:
        return 0
    
    reading_time = max(1, round(word_count / words_per_minute))
    return reading_time


def highlight_text(text: str, search_term: str) -> str:
    """
    Highlight search terms in text.
    
    Args:
        text: The text to search in
        search_term: The term to highlight
        
    Returns:
        str: Text with highlighted search terms
    """
    if not text or not search_term:
        return text
    
    # Escape HTML in search term
    escaped_term = html.escape(search_term)
    
    # Case-insensitive replacement
    pattern = re.escape(search_term)
    highlighted = re.sub(
        f'({pattern})', 
        r'<mark>\1</mark>', 
        text, 
        flags=re.IGNORECASE
    )
    
    return highlighted


def truncate_text(text: str, max_length: int, suffix: str = '...', word_boundary: bool = False) -> str:
    """
    Truncate text to a maximum length.
    
    Args:
        text: The text to truncate
        max_length: Maximum length of the result
        suffix: Suffix to add when truncating
        word_boundary: Whether to truncate at word boundaries
        
    Returns:
        str: Truncated text
    """
    if not text or len(text) <= max_length:
        return text
    
    if word_boundary:
        # Find the last space before max_length
        truncate_at = text.rfind(' ', 0, max_length - len(suffix))
        if truncate_at == -1:
            truncate_at = max_length - len(suffix)
    else:
        truncate_at = max_length - len(suffix)
    
    return text[:truncate_at] + suffix


def capitalize_words(text: str) -> str:
    """
    Capitalize the first letter of each word.
    
    Args:
        text: The text to capitalize
        
    Returns:
        str: Text with capitalized words
    """
    if not text:
        return ""
    
    return ' '.join(word.capitalize() for word in text.split())


def remove_extra_spaces(text: str) -> str:
    """
    Remove extra whitespace from text.
    
    Args:
        text: The text to clean
        
    Returns:
        str: Text with normalized whitespace
    """
    if not text:
        return ""
    
    # Replace multiple whitespace characters with single space
    cleaned = re.sub(r'\s+', ' ', text)
    return cleaned.strip()


def convert_to_slug(text: str) -> str:
    """
    Convert text to a URL-friendly slug.
    
    Args:
        text: The text to convert
        
    Returns:
        str: URL-friendly slug
    """
    if not text:
        return ""
    
    # Convert to lowercase
    slug = text.lower()
    
    # Remove accents and special characters
    slug = re.sub(r'[àáâãäå]', 'a', slug)
    slug = re.sub(r'[èéêë]', 'e', slug)
    slug = re.sub(r'[ìíîï]', 'i', slug)
    slug = re.sub(r'[òóôõö]', 'o', slug)
    slug = re.sub(r'[ùúûü]', 'u', slug)
    slug = re.sub(r'[ñ]', 'n', slug)
    slug = re.sub(r'[ç]', 'c', slug)
    
    # Replace non-alphanumeric characters with hyphens
    slug = re.sub(r'[^a-z0-9]+', '-', slug)
    
    # Remove leading/trailing hyphens and multiple consecutive hyphens
    slug = re.sub(r'^-+|-+$', '', slug)
    slug = re.sub(r'-+', '-', slug)
    
    return slug
