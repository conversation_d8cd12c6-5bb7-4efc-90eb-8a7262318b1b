"""
Validation utility functions
"""
import re
from typing import Dict, Any


def validate_email(email: str) -> bool:
    """
    Validate email address format.
    
    Args:
        email: Email address to validate
        
    Returns:
        bool: True if email is valid, False otherwise
    """
    if not email:
        return False
    
    # Basic email regex pattern
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def validate_password_strength(password: str) -> Dict[str, Any]:
    """
    Validate password strength and return detailed feedback.
    
    Args:
        password: Password to validate
        
    Returns:
        Dict containing validation results
    """
    result = {
        "is_valid": False,
        "score": 0,
        "feedback": []
    }
    
    if not password:
        result["feedback"].append("Password is required")
        return result
    
    score = 0
    feedback = []
    
    # Length check
    if len(password) >= 8:
        score += 1
    else:
        feedback.append("Password must be at least 8 characters long")
    
    # Uppercase letter check
    if re.search(r'[A-Z]', password):
        score += 1
    else:
        feedback.append("Password must contain at least one uppercase letter")
    
    # Lowercase letter check
    if re.search(r'[a-z]', password):
        score += 1
    else:
        feedback.append("Password must contain at least one lowercase letter")
    
    # Number check
    if re.search(r'\d', password):
        score += 1
    else:
        feedback.append("Password must contain at least one number")
    
    # Special character check
    if re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
        score += 1
    else:
        feedback.append("Password must contain at least one special character")
    
    result["score"] = score
    result["feedback"] = feedback
    result["is_valid"] = score >= 3  # Require at least 3 criteria
    
    return result
