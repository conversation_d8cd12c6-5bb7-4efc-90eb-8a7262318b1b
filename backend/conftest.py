"""
Test configuration and fixtures for the WritingWay backend
"""
import pytest
import asyncio
from typing import Generator, AsyncGenerator
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
import tempfile
import os

from main import app
from database.database import get_db, Base
from database.models import User, Project, Document
from core.security import create_access_token, get_password_hash
from services.ai_service import AIService


# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
def db_session():
    """Create a fresh database session for each test."""
    Base.metadata.create_all(bind=engine)
    session = TestingSessionLocal()
    try:
        yield session
    finally:
        session.close()
        Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def client(db_session):
    """Create a test client with database dependency override."""
    def override_get_db():
        try:
            yield db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    with TestClient(app) as test_client:
        yield test_client
    app.dependency_overrides.clear()


@pytest.fixture
def test_user_data():
    """Test user data."""
    return {
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "testpassword123",
        "full_name": "Test User",
        "age_group": "adult"
    }


@pytest.fixture
def test_user(db_session, test_user_data):
    """Create a test user in the database."""
    user = User(
        username=test_user_data["username"],
        email=test_user_data["email"],
        hashed_password=get_password_hash(test_user_data["password"]),
        full_name=test_user_data["full_name"],
        age_group=test_user_data["age_group"],
        is_active=True
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture
def auth_headers(test_user):
    """Create authentication headers for test user."""
    access_token = create_access_token(data={"sub": test_user.username})
    return {"Authorization": f"Bearer {access_token}"}


@pytest.fixture
def test_project(db_session, test_user):
    """Create a test project."""
    project = Project(
        name="Test Project",
        description="A test project for testing",
        owner_id=test_user.id
    )
    db_session.add(project)
    db_session.commit()
    db_session.refresh(project)
    return project


@pytest.fixture
def test_document(db_session, test_project):
    """Create a test document."""
    document = Document(
        title="Test Document",
        content="This is test content for the document.",
        document_type="scene",
        project_id=test_project.id
    )
    db_session.add(document)
    db_session.commit()
    db_session.refresh(document)
    return document


@pytest.fixture
def mock_ai_service(monkeypatch):
    """Mock AI service for testing."""
    class MockAIService:
        def __init__(self):
            self.is_available = True
        
        async def chat(self, messages, context=None):
            return "This is a mock AI response for testing purposes."
        
        async def get_writing_assistance(self, text, assistance_type, age_group="adult"):
            return f"Mock {assistance_type} assistance for: {text[:50]}..."
        
        async def generate_story_prompt(self, genre=None, age_group="adult"):
            return "Mock story prompt: Write about a brave character on an adventure."
        
        async def get_realtime_suggestions(self, text, context=None):
            return ["Mock suggestion 1", "Mock suggestion 2", "Mock suggestion 3"]
    
    mock_service = MockAIService()
    monkeypatch.setattr("services.ai_service.ai_service", mock_service)
    return mock_service


@pytest.fixture
def temp_upload_dir():
    """Create a temporary directory for file uploads."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield temp_dir


# Pytest markers for different test types
pytestmark = pytest.mark.asyncio
