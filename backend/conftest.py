"""
Test configuration and fixtures for the WritingWay backend
"""
import pytest
import asyncio
from typing import Generator, AsyncGenerator
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
import tempfile
import os

from main import app
from database.database import get_db, Base
from database.models import User, Project, Document
from core.security import create_access_token, get_password_hash
from services.ai_service import AIService


# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    """Override database dependency for testing"""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
def db_session():
    """Create a fresh database session for each test"""
    Base.metadata.create_all(bind=engine)
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()
        Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def client(db_session):
    """Create a test client with database override"""
    app.dependency_overrides[get_db] = override_get_db
    with TestClient(app) as test_client:
        yield test_client
    app.dependency_overrides.clear()


@pytest.fixture
def test_user(db_session):
    """Create a test user"""
    user = User(
        username="testuser",
        email="<EMAIL>",
        hashed_password=get_password_hash("testpassword"),
        full_name="Test User",
        age_group="adult"
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture
def test_admin_user(db_session):
    """Create a test admin user"""
    user = User(
        username="admin",
        email="<EMAIL>",
        hashed_password=get_password_hash("admin123"),
        full_name="Admin User",
        age_group="adult",
        is_admin=True
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture
def test_project(db_session, test_user):
    """Create a test project"""
    project = Project(
        name="Test Project",
        description="A test project for testing",
        owner_id=test_user.id
    )
    db_session.add(project)
    db_session.commit()
    db_session.refresh(project)
    return project


@pytest.fixture
def test_document(db_session, test_project):
    """Create a test document"""
    document = Document(
        title="Test Document",
        content="This is test content for the document.",
        document_type="scene",
        project_id=test_project.id
    )
    db_session.add(document)
    db_session.commit()
    db_session.refresh(document)
    return document


@pytest.fixture
def auth_headers(test_user):
    """Create authentication headers for test user"""
    access_token = create_access_token(data={"sub": test_user.username})
    return {"Authorization": f"Bearer {access_token}"}


@pytest.fixture
def admin_auth_headers(test_admin_user):
    """Create authentication headers for admin user"""
    access_token = create_access_token(data={"sub": test_admin_user.username})
    return {"Authorization": f"Bearer {access_token}"}


@pytest.fixture
def mock_ai_service():
    """Create a mock AI service for testing"""
    class MockAIService:
        def chat(self, messages, context=None, task_type='chat'):
            return "This is a mock AI response for testing purposes."
        
        def get_writing_assistance(self, text, assistance_type, age_group=None):
            return f"Mock {assistance_type} assistance for: {text[:50]}..."
        
        def generate_story_prompt(self, genre=None, age_group=None):
            return "Mock story prompt: Write about a brave character on an adventure."
    
    return MockAIService()


@pytest.fixture
def temp_upload_dir():
    """Create a temporary directory for file uploads"""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield temp_dir


# Test data fixtures
@pytest.fixture
def sample_chat_messages():
    """Sample chat messages for testing"""
    return [
        {"role": "user", "content": "Help me write a story about dragons"},
        {"role": "assistant", "content": "I'd be happy to help you write a dragon story!"}
    ]


@pytest.fixture
def sample_project_data():
    """Sample project data for testing"""
    return {
        "name": "Fantasy Adventure",
        "description": "An epic fantasy story with dragons and magic",
        "cover_image": "https://example.com/cover.jpg"
    }


@pytest.fixture
def sample_document_data():
    """Sample document data for testing"""
    return {
        "title": "Chapter 1: The Beginning",
        "content": "Once upon a time, in a land far away...",
        "document_type": "chapter"
    }


@pytest.fixture
def sample_user_data():
    """Sample user registration data"""
    return {
        "username": "newuser",
        "email": "<EMAIL>",
        "password": "newpassword123",
        "full_name": "New User"
    }
