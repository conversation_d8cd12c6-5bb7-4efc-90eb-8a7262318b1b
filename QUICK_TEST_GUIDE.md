# 快速测试指南

## 测试报告位置说明

测试报告只有在运行测试后才会生成。以下是各种报告的生成位置：

### 📊 后端测试报告

#### 1. 覆盖率报告 (HTML)
- **位置**: `backend/htmlcov/index.html`
- **生成命令**: 
  ```bash
  cd backend
  pytest --cov=. --cov-report=html
  ```
- **说明**: 这是一个可视化的 HTML 报告，显示代码覆盖率详情

#### 2. 覆盖率报告 (XML)
- **位置**: `backend/coverage.xml`
- **用途**: CI/CD 系统使用，如 Codecov

#### 3. 测试结果报告
- **位置**: 终端输出 + `backend/test-results.xml` (如果配置)

### 📊 前端测试报告

#### 1. Jest 覆盖率报告 (HTML)
- **位置**: `frontend/coverage/lcov-report/index.html`
- **生成命令**:
  ```bash
  cd frontend
  npm run test:coverage
  ```

#### 2. Jest 覆盖率报告 (LCOV)
- **位置**: `frontend/coverage/lcov.info`
- **用途**: CI/CD 系统使用

#### 3. Cypress 测试报告
- **位置**: `frontend/cypress/reports/` (如果配置)
- **视频**: `frontend/cypress/videos/`
- **截图**: `frontend/cypress/screenshots/`

### 📋 综合测试报告
- **位置**: `test-reports/test-report.md`
- **生成**: 运行 `python run_tests.py` 后自动生成

## 🚀 快速开始测试

### 1. 安装依赖

#### 后端依赖
```bash
cd backend
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

pip install -r requirements.txt
```

#### 前端依赖
```bash
cd frontend
npm install
```

### 2. 运行简单测试

#### 后端单元测试
```bash
cd backend
# 激活虚拟环境后
pytest tests/unit/test_auth.py -v
```

#### 前端单元测试
```bash
cd frontend
npm test -- --watchAll=false
```

### 3. 生成覆盖率报告

#### 后端覆盖率
```bash
cd backend
pytest --cov=. --cov-report=html --cov-report=term-missing
```
运行后查看: `backend/htmlcov/index.html`

#### 前端覆盖率
```bash
cd frontend
npm run test:coverage
```
运行后查看: `frontend/coverage/lcov-report/index.html`

### 4. 使用统一测试脚本

```bash
# 运行所有测试并生成报告
python run_tests.py --all

# 只运行后端测试
python run_tests.py --backend

# 只运行前端测试
python run_tests.py --frontend

# Windows 用户
run_tests.bat --all
```

## 📁 测试文件位置

### 后端测试文件
```
backend/tests/
├── unit/                     # 单元测试
│   ├── test_auth.py         # ✅ 已创建
│   ├── test_ai_service.py   # ✅ 已创建
│   ├── test_models.py       # ✅ 已创建
│   └── test_utils.py        # ✅ 已创建
├── integration/             # 集成测试
│   ├── test_auth_api.py     # ✅ 已创建
│   ├── test_projects_api.py # ✅ 已创建
│   └── test_ai_api.py       # ✅ 已创建
└── e2e/                     # 端到端测试
    └── test_user_workflows.py # ✅ 已创建
```

### 前端测试文件
```
frontend/src/__tests__/
├── components/              # 组件测试
│   ├── Auth/
│   │   └── LoginForm.test.js # ✅ 已创建
│   └── AI/
│       └── AIAssistant.test.js # ✅ 已创建
├── stores/                  # 状态管理测试
│   └── authStore.test.js    # ✅ 已创建
├── pages/                   # 页面测试
│   └── Dashboard/
│       └── Dashboard.test.js # ✅ 已创建
└── utils/                   # 工具函数测试
    └── textUtils.test.js    # ✅ 已创建

frontend/cypress/e2e/        # E2E 测试
├── user-authentication.cy.js # ✅ 已创建
├── writing-workflow.cy.js   # ✅ 已创建
└── ai-features.cy.js        # ✅ 已创建
```

## 🔧 配置文件

### 后端配置
- `backend/conftest.py` - pytest fixtures 和配置
- `backend/pytest.ini` - pytest 设置
- `backend/requirements.txt` - 包含测试依赖

### 前端配置
- `frontend/src/setupTests.js` - Jest 配置
- `frontend/cypress.config.js` - Cypress 配置
- `frontend/package.json` - 测试脚本

## ⚠️ 注意事项

1. **报告生成**: 测试报告只有在运行测试后才会生成
2. **依赖安装**: 确保先安装所有依赖
3. **虚拟环境**: 后端测试需要激活 Python 虚拟环境
4. **数据库**: 某些测试可能需要数据库连接

## 🐛 常见问题

### 1. 找不到测试报告
**原因**: 还没有运行过测试
**解决**: 先运行测试命令生成报告

### 2. 模块导入错误
**原因**: 依赖未安装或虚拟环境未激活
**解决**: 检查依赖安装和环境激活

### 3. 数据库连接错误
**原因**: 测试数据库配置问题
**解决**: 检查数据库配置或使用 SQLite 测试

## 📞 获取帮助

如果遇到问题，可以：
1. 查看 `TEST_DOCUMENTATION.md` 详细文档
2. 检查测试配置文件
3. 查看错误日志输出

## 🎯 下一步

1. 运行一个简单的测试验证环境
2. 查看生成的测试报告
3. 根据需要调整测试配置
4. 添加更多测试用例
