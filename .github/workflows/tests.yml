name: Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  PYTHON_VERSION: '3.11'
  NODE_VERSION: '18'

jobs:
  backend-tests:
    name: Backend Tests
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: testpassword
          MYSQL_DATABASE: ai_syory_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Cache Python dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('backend/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Install Python dependencies
      run: |
        cd backend
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Set up test environment
      run: |
        cd backend
        cp .env.example .env
        echo "DATABASE_URL=mysql+pymysql://root:testpassword@localhost:3306/ai_syory_test" >> .env
        echo "TESTING=true" >> .env

    - name: Run backend unit tests
      run: |
        cd backend
        pytest tests/unit/ --cov=. --cov-report=xml --cov-report=term-missing -v

    - name: Run backend integration tests
      run: |
        cd backend
        pytest tests/integration/ --cov=. --cov-append --cov-report=xml --cov-report=term-missing -v

    - name: Upload backend coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage.xml
        flags: backend
        name: backend-coverage

  frontend-tests:
    name: Frontend Tests
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json

    - name: Install frontend dependencies
      run: |
        cd frontend
        npm ci

    - name: Run frontend unit tests
      run: |
        cd frontend
        npm run test:coverage

    - name: Upload frontend coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./frontend/coverage/lcov.info
        flags: frontend
        name: frontend-coverage

  e2e-tests:
    name: E2E Tests
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]

    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: testpassword
          MYSQL_DATABASE: ai_syory_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json

    - name: Cache Python dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('backend/requirements.txt') }}

    - name: Install dependencies
      run: |
        # Backend dependencies
        cd backend
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        cd ..
        
        # Frontend dependencies
        cd frontend
        npm ci
        cd ..

    - name: Set up test environment
      run: |
        cd backend
        cp .env.example .env
        echo "DATABASE_URL=mysql+pymysql://root:testpassword@localhost:3306/ai_syory_test" >> .env
        echo "TESTING=true" >> .env
        
        # Initialize test database
        python init_db.py
        python create_test_users.py

    - name: Start backend server
      run: |
        cd backend
        python -m uvicorn main:app --host 0.0.0.0 --port 8000 &
        sleep 10
      
    - name: Start frontend server
      run: |
        cd frontend
        npm start &
        sleep 15

    - name: Run Cypress E2E tests
      uses: cypress-io/github-action@v6
      with:
        working-directory: frontend
        wait-on: 'http://localhost:3000, http://localhost:8000'
        wait-on-timeout: 120
        browser: chrome
        record: true
        parallel: true
      env:
        CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY }}
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    - name: Upload Cypress screenshots
      uses: actions/upload-artifact@v3
      if: failure()
      with:
        name: cypress-screenshots
        path: frontend/cypress/screenshots

    - name: Upload Cypress videos
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: cypress-videos
        path: frontend/cypress/videos

  security-tests:
    name: Security Tests
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Run Bandit security linter
      run: |
        pip install bandit
        bandit -r backend/ -f json -o bandit-report.json || true

    - name: Run npm audit
      run: |
        cd frontend
        npm audit --audit-level=moderate || true

    - name: Upload security reports
      uses: actions/upload-artifact@v3
      with:
        name: security-reports
        path: |
          bandit-report.json
          frontend/npm-audit.json

  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install dependencies
      run: |
        cd backend
        pip install -r requirements.txt
        pip install locust

    - name: Run performance tests
      run: |
        cd backend
        # Run basic performance tests
        python -m pytest tests/performance/ --benchmark-only || true

  code-quality:
    name: Code Quality
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}

    - name: Install Python linting tools
      run: |
        pip install flake8 black isort mypy

    - name: Run Python linting
      run: |
        cd backend
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        black --check .
        isort --check-only .
        mypy . --ignore-missing-imports || true

    - name: Install frontend linting tools
      run: |
        cd frontend
        npm ci

    - name: Run frontend linting
      run: |
        cd frontend
        npm run lint || true

  build-test:
    name: Build Test
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json

    - name: Install dependencies
      run: |
        cd frontend
        npm ci

    - name: Build frontend
      run: |
        cd frontend
        npm run build

    - name: Test Docker build
      run: |
        docker build -t writingway-backend ./backend
        docker build -t writingway-frontend ./frontend

  test-summary:
    name: Test Summary
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests, e2e-tests]
    if: always()

    steps:
    - name: Check test results
      run: |
        echo "Backend tests: ${{ needs.backend-tests.result }}"
        echo "Frontend tests: ${{ needs.frontend-tests.result }}"
        echo "E2E tests: ${{ needs.e2e-tests.result }}"
        
        if [[ "${{ needs.backend-tests.result }}" == "failure" || "${{ needs.frontend-tests.result }}" == "failure" || "${{ needs.e2e-tests.result }}" == "failure" ]]; then
          echo "Some tests failed!"
          exit 1
        else
          echo "All tests passed!"
        fi
