@echo off
REM Windows batch script to run WritingWay tests
REM Usage: run_tests.bat [options]

setlocal enabledelayedexpansion

REM Default values
set RUN_BACKEND=0
set RUN_FRONTEND=0
set RUN_INTEGRATION=0
set RUN_ALL=0
set RUN_UNIT=0
set RUN_E2E=0
set NO_COVERAGE=0
set SETUP_ONLY=0

REM Parse command line arguments
:parse_args
if "%1"=="" goto end_parse
if "%1"=="--backend" set RUN_BACKEND=1
if "%1"=="--frontend" set RUN_FRONTEND=1
if "%1"=="--integration" set RUN_INTEGRATION=1
if "%1"=="--all" set RUN_ALL=1
if "%1"=="--unit" set RUN_UNIT=1
if "%1"=="--e2e" set RUN_E2E=1
if "%1"=="--no-coverage" set NO_COVERAGE=1
if "%1"=="--setup-only" set SETUP_ONLY=1
if "%1"=="--help" goto show_help
shift
goto parse_args

:end_parse

REM Default to running all tests if no specific option is provided
if %RUN_BACKEND%==0 if %RUN_FRONTEND%==0 if %RUN_INTEGRATION%==0 set RUN_ALL=1

echo.
echo ========================================
echo WritingWay Test Runner
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.8 or higher
    exit /b 1
)

REM Check if Node.js is available
node --version >nul 2>&1
if errorlevel 1 (
    echo Error: Node.js is not installed or not in PATH
    echo Please install Node.js 16 or higher
    exit /b 1
)

REM Setup backend environment
if %RUN_BACKEND%==1 goto setup_backend
if %RUN_ALL%==1 goto setup_backend
if %RUN_INTEGRATION%==1 goto setup_backend
goto skip_backend_setup

:setup_backend
echo Setting up backend environment...
cd backend

REM Check if virtual environment exists
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo Error: Failed to create virtual environment
        exit /b 1
    )
)

REM Activate virtual environment and install dependencies
echo Installing backend dependencies...
call venv\Scripts\activate.bat
pip install -r requirements.txt
if errorlevel 1 (
    echo Error: Failed to install backend dependencies
    exit /b 1
)

cd ..

:skip_backend_setup

REM Setup frontend environment
if %RUN_FRONTEND%==1 goto setup_frontend
if %RUN_ALL%==1 goto setup_frontend
if %RUN_INTEGRATION%==1 goto setup_frontend
goto skip_frontend_setup

:setup_frontend
echo Setting up frontend environment...
cd frontend

REM Check if node_modules exists
if not exist "node_modules" (
    echo Installing frontend dependencies...
    npm install
    if errorlevel 1 (
        echo Error: Failed to install frontend dependencies
        exit /b 1
    )
)

cd ..

:skip_frontend_setup

if %SETUP_ONLY%==1 (
    echo Environment setup completed successfully!
    exit /b 0
)

REM Run tests
set TEST_SUCCESS=1

REM Run backend tests
if %RUN_BACKEND%==1 goto run_backend_tests
if %RUN_ALL%==1 goto run_backend_tests
goto skip_backend_tests

:run_backend_tests
echo.
echo Running backend tests...
cd backend
call venv\Scripts\activate.bat

set PYTEST_CMD=pytest
if %RUN_UNIT%==1 set PYTEST_CMD=pytest tests/unit/
if %RUN_E2E%==1 set PYTEST_CMD=pytest tests/e2e/

if %NO_COVERAGE%==0 (
    %PYTEST_CMD% --cov=. --cov-report=html:htmlcov --cov-report=term-missing --cov-report=xml -v --tb=short
) else (
    %PYTEST_CMD% -v --tb=short
)

if errorlevel 1 (
    echo Backend tests failed!
    set TEST_SUCCESS=0
) else (
    echo Backend tests passed!
    if %NO_COVERAGE%==0 echo Coverage report generated at: backend\htmlcov\index.html
)

cd ..

:skip_backend_tests

REM Run frontend tests
if %RUN_FRONTEND%==1 goto run_frontend_tests
if %RUN_ALL%==1 goto run_frontend_tests
goto skip_frontend_tests

:run_frontend_tests
echo.
echo Running frontend tests...
cd frontend

REM Run unit tests
if %RUN_UNIT%==1 goto run_frontend_unit
if %RUN_ALL%==1 goto run_frontend_unit
goto skip_frontend_unit

:run_frontend_unit
echo Running frontend unit tests...
set CI=true
if %NO_COVERAGE%==0 (
    npm run test:coverage
) else (
    npm run test:ci
)

if errorlevel 1 (
    echo Frontend unit tests failed!
    set TEST_SUCCESS=0
) else (
    echo Frontend unit tests passed!
    if %NO_COVERAGE%==0 echo Coverage report generated at: frontend\coverage\lcov-report\index.html
)

:skip_frontend_unit

REM Run E2E tests
if %RUN_E2E%==1 goto run_frontend_e2e
if %RUN_ALL%==1 goto run_frontend_e2e
goto skip_frontend_e2e

:run_frontend_e2e
echo Running frontend E2E tests...
echo Starting development server...

REM Start development server in background
start /b npm start
timeout /t 10 /nobreak >nul

REM Run Cypress tests
npm run cypress:run

if errorlevel 1 (
    echo Frontend E2E tests failed!
    set TEST_SUCCESS=0
) else (
    echo Frontend E2E tests passed!
)

REM Stop development server
taskkill /f /im node.exe >nul 2>&1

:skip_frontend_e2e

cd ..

:skip_frontend_tests

REM Run integration tests
if %RUN_INTEGRATION%==1 goto run_integration_tests
goto skip_integration_tests

:run_integration_tests
echo.
echo Running integration tests...

REM Start backend server
echo Starting backend server...
cd backend
call venv\Scripts\activate.bat
start /b python -m uvicorn main:app --host 0.0.0.0 --port 8000
cd ..

REM Start frontend server
echo Starting frontend server...
cd frontend
start /b npm start
cd ..

REM Wait for servers to start
echo Waiting for servers to start...
timeout /t 15 /nobreak >nul

REM Run integration tests
cd backend
call venv\Scripts\activate.bat
pytest tests/integration/ -v --tb=short

if errorlevel 1 (
    echo Integration tests failed!
    set TEST_SUCCESS=0
) else (
    echo Integration tests passed!
)

cd ..

REM Stop servers
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im node.exe >nul 2>&1

:skip_integration_tests

REM Generate test report
echo.
echo Generating test report...
if not exist "test-reports" mkdir test-reports

echo # WritingWay Test Report > test-reports\test-report.md
echo. >> test-reports\test-report.md
echo Generated at: %date% %time% >> test-reports\test-report.md
echo. >> test-reports\test-report.md

if exist "backend\htmlcov\index.html" (
    echo ## Backend Coverage >> test-reports\test-report.md
    echo - Coverage report: backend\htmlcov\index.html >> test-reports\test-report.md
    echo. >> test-reports\test-report.md
)

if exist "frontend\coverage\lcov-report\index.html" (
    echo ## Frontend Coverage >> test-reports\test-report.md
    echo - Coverage report: frontend\coverage\lcov-report\index.html >> test-reports\test-report.md
    echo. >> test-reports\test-report.md
)

echo ## Test Locations >> test-reports\test-report.md
echo ### Backend Tests >> test-reports\test-report.md
echo - Unit tests: backend\tests\unit\ >> test-reports\test-report.md
echo - Integration tests: backend\tests\integration\ >> test-reports\test-report.md
echo - E2E tests: backend\tests\e2e\ >> test-reports\test-report.md
echo. >> test-reports\test-report.md
echo ### Frontend Tests >> test-reports\test-report.md
echo - Unit tests: frontend\src\__tests__\ >> test-reports\test-report.md
echo - E2E tests: frontend\cypress\e2e\ >> test-reports\test-report.md

echo Test report generated: test-reports\test-report.md

REM Final result
echo.
echo ========================================
if %TEST_SUCCESS%==1 (
    echo All tests completed successfully!
    echo ========================================
    exit /b 0
) else (
    echo Some tests failed!
    echo ========================================
    exit /b 1
)

:show_help
echo Usage: run_tests.bat [options]
echo.
echo Options:
echo   --backend      Run backend tests only
echo   --frontend     Run frontend tests only
echo   --integration  Run integration tests only
echo   --all          Run all tests (default)
echo   --unit         Run unit tests only
echo   --e2e          Run E2E tests only
echo   --no-coverage  Skip coverage reporting
echo   --setup-only   Only setup environments
echo   --help         Show this help message
echo.
echo Examples:
echo   run_tests.bat --all
echo   run_tests.bat --backend --unit
echo   run_tests.bat --frontend --e2e
echo   run_tests.bat --setup-only
exit /b 0
