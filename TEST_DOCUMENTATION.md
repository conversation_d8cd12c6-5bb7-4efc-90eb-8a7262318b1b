# WritingWay 测试文档

## 概述

本文档详细说明了 WritingWay 项目的完整测试套件，包括前端、后端、集成测试和端到端测试。

## 测试架构

### 后端测试 (Python/FastAPI)

#### 1. 单元测试 (`backend/tests/unit/`)
- **位置**: `backend/tests/unit/`
- **框架**: pytest, pytest-asyncio
- **覆盖范围**:
  - 认证和安全功能 (`test_auth.py`)
  - AI 服务功能 (`test_ai_service.py`)
  - 数据库模型 (`test_models.py`)
  - 核心业务逻辑

#### 2. 集成测试 (`backend/tests/integration/`)
- **位置**: `backend/tests/integration/`
- **框架**: pytest + FastAPI TestClient
- **覆盖范围**:
  - API 端点测试 (`test_auth_api.py`, `test_projects_api.py`, `test_ai_api.py`)
  - 数据库集成
  - 外部服务集成

#### 3. 端到端测试 (`backend/tests/e2e/`)
- **位置**: `backend/tests/e2e/`
- **框架**: pytest + httpx
- **覆盖范围**:
  - 完整用户工作流程 (`test_user_workflows.py`)
  - 跨服务交互
  - 真实场景模拟

### 前端测试 (React)

#### 1. 单元测试 (`frontend/src/__tests__/`)
- **位置**: `frontend/src/__tests__/`
- **框架**: Jest + React Testing Library
- **覆盖范围**:
  - 组件测试 (`components/`)
  - 状态管理测试 (`stores/`)
  - 工具函数测试 (`utils/`)

#### 2. 集成测试
- **位置**: `frontend/src/__tests__/pages/`
- **框架**: Jest + React Testing Library + MSW
- **覆盖范围**:
  - 页面级组件测试
  - API 集成测试
  - 用户交互流程

#### 3. 端到端测试 (`frontend/cypress/`)
- **位置**: `frontend/cypress/e2e/`
- **框架**: Cypress
- **覆盖范围**:
  - 用户认证流程 (`user-authentication.cy.js`)
  - 完整写作工作流程 (`writing-workflow.cy.js`)
  - 跨浏览器兼容性

## 测试配置文件

### 后端配置
- `backend/conftest.py` - pytest 配置和 fixtures
- `backend/pytest.ini` - pytest 设置
- `backend/requirements.txt` - 包含测试依赖

### 前端配置
- `frontend/src/setupTests.js` - Jest 配置和 MSW 设置
- `frontend/cypress.config.js` - Cypress 配置
- `frontend/cypress/support/commands.js` - 自定义 Cypress 命令
- `frontend/package.json` - 测试脚本和依赖

## 运行测试

### 使用统一测试脚本

```bash
# 运行所有测试
python run_tests.py --all

# 只运行后端测试
python run_tests.py --backend

# 只运行前端测试
python run_tests.py --frontend

# 运行集成测试
python run_tests.py --integration

# 运行单元测试
python run_tests.py --unit

# 运行端到端测试
python run_tests.py --e2e

# 跳过覆盖率报告
python run_tests.py --all --no-coverage

# 只设置环境
python run_tests.py --setup-only
```

### 单独运行测试

#### 后端测试
```bash
cd backend

# 激活虚拟环境
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 运行所有测试
pytest

# 运行特定类型的测试
pytest tests/unit/
pytest tests/integration/
pytest tests/e2e/

# 运行特定测试文件
pytest tests/unit/test_auth.py

# 运行带覆盖率的测试
pytest --cov=. --cov-report=html
```

#### 前端测试
```bash
cd frontend

# 运行单元测试
npm test

# 运行覆盖率测试
npm run test:coverage

# 运行 CI 模式测试
npm run test:ci

# 运行 Cypress E2E 测试
npm run cypress:open  # 交互模式
npm run cypress:run   # 无头模式
```

## 测试覆盖率

### 后端覆盖率
- **目标**: 80% 以上
- **报告位置**: `backend/htmlcov/index.html`
- **XML 报告**: `backend/coverage.xml`

### 前端覆盖率
- **目标**: 80% 以上
- **报告位置**: `frontend/coverage/lcov-report/index.html`
- **LCOV 报告**: `frontend/coverage/lcov.info`

## 测试数据和 Fixtures

### 后端 Fixtures (`backend/conftest.py`)
- `test_user` - 测试用户
- `test_admin_user` - 管理员用户
- `test_project` - 测试项目
- `test_document` - 测试文档
- `auth_headers` - 认证头
- `mock_ai_service` - 模拟 AI 服务

### 前端 Mock 数据 (`frontend/src/setupTests.js`)
- MSW (Mock Service Worker) 配置
- API 端点模拟
- 本地存储模拟
- 浏览器 API 模拟

## 持续集成 (CI)

### GitHub Actions 配置示例
```yaml
name: Tests
on: [push, pull_request]

jobs:
  backend-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      - run: python run_tests.py --backend

  frontend-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: python run_tests.py --frontend

  integration-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - run: python run_tests.py --integration
```

## 测试最佳实践

### 1. 测试命名
- 使用描述性的测试名称
- 遵循 `test_should_do_something_when_condition` 格式

### 2. 测试隔离
- 每个测试应该独立运行
- 使用 fixtures 和 setup/teardown 确保清洁状态

### 3. 模拟外部依赖
- 使用 mock 对象模拟外部 API
- 避免在测试中进行真实的网络请求

### 4. 测试数据
- 使用工厂函数创建测试数据
- 避免硬编码测试数据

### 5. 断言
- 使用具体的断言而不是通用的
- 提供有意义的错误消息

## 性能测试

### 后端性能测试
```bash
# 使用 pytest-benchmark
pytest tests/performance/ --benchmark-only
```

### 前端性能测试
```javascript
// 在 Cypress 中测量性能
cy.measurePerformance('page-load').end();
```

## 可访问性测试

### 前端可访问性
```javascript
// 在 Cypress 中检查可访问性
cy.checkA11y();
```

## 视觉回归测试

### Cypress 视觉测试
```javascript
// 截图对比
cy.compareSnapshot('login-page');
```

## 故障排除

### 常见问题

1. **测试数据库连接失败**
   - 检查数据库配置
   - 确保测试数据库存在

2. **前端测试超时**
   - 增加超时时间
   - 检查异步操作

3. **Cypress 测试不稳定**
   - 添加适当的等待
   - 使用 data-testid 属性

4. **覆盖率不足**
   - 识别未覆盖的代码
   - 添加缺失的测试用例

## 测试报告

测试完成后，会在 `test-reports/` 目录生成综合报告：
- `test-report.md` - 测试总结报告
- 覆盖率报告链接
- 测试运行统计

## 维护和更新

### 定期维护任务
1. 更新测试依赖
2. 审查和更新测试用例
3. 检查测试覆盖率
4. 优化测试性能

### 添加新测试
1. 为新功能添加单元测试
2. 更新集成测试
3. 添加 E2E 测试场景
4. 更新文档

## 联系信息

如有测试相关问题，请联系开发团队或查看项目文档。
