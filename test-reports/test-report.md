# WritingWay Test Report
Generated at: 2025-07-26 23:53:59

## Backend Coverage
- Coverage report: C:\Users\<USER>\PycharmProjects\COMP9900-W10A-Bread-LI\backend/htmlcov/index.html
- XML report: C:\Users\<USER>\PycharmProjects\COMP9900-W10A-Bread-LI\backend\coverage.xml

## Test Locations
### Backend Tests
- Unit tests: `backend/tests/unit/`
- Integration tests: `backend/tests/integration/`
- E2E tests: `backend/tests/e2e/`

### Frontend Tests
- Unit tests: `frontend/src/__tests__/`
- E2E tests: `frontend/cypress/e2e/`

## Running Tests
```bash
# Run all tests
python run_tests.py --all

# Run specific test types
python run_tests.py --backend --unit
python run_tests.py --frontend --e2e
```
